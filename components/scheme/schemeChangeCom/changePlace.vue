<script setup lang="ts">
// 方案变更-会场方案
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, nextTick, inject, defineProps, defineEmits, defineExpose } from 'vue';

import { errorModal, resolveParam, routerParam, formatNumberThousands } from '@haierbusiness-front/utils';
import {
  hotelLevelAllConstant,
  PlaceUsageTimeTypeConstant,
  UsagePurposeTypeConstant,
  TableTypeConstant,
  HotelsArr,
  PlacesArr,
  LedSourceTypeConstant,
} from '@haierbusiness-front/common-libs';

import schemeGuildhall from './../schemeGuildhall.vue';

const props = defineProps({
  schemeItem: {
    type: Object,
    default: {},
  },
  schemeCacheItem: {
    type: Object,
    default: {},
  },
  schemeChangeType: {
    // schemeBargainingEdit - 方案议价, schemeBargainingView - 议价查看, schemeChangeEdit - 方案变更, schemeChangeView - 方案变更查看, schemeWinBidView - 方案中标查看
    type: String,
    default: '',
  },
  isSchemeCache: {
    type: Boolean,
    default: false,
  },
  isSchemeBargain: {
    // 是否议价
    type: Boolean,
    default: false,
  },
  hotels: {
    type: Array,
    default: [],
  },
  schemeIndex: {
    type: Number,
    default: 0,
  },
  processNode: {
    type: String,
    default: '',
  },
  merchantType: {
    type: Number,
    default: null,
  },
});

const emit = defineEmits(['schemePriceEmit', 'schemePlacesEmit']);

const schemeChangeAddFunc = inject('schemeChangeAddFunc', () => {});
const schemeChangeDelFunc = inject('schemeChangeDelFunc', () => {});

const oldSchemeList = ref<array>([]);
const newSchemeList = ref<array>([]);

const subtotal = ref<number>(0); // 小计

const isVerifyFailed = ref<boolean>(false); // 校验是否失败

// 会场
const placesParams = ref<PlacesArr>({
  tempSchemeHotelId: null,
  level: null,
  demandDate: '', // 方案日期
  usageTime: null, // 使用时间
  usagePurpose: null, // 使用用途
  schemePersonNum: null, // 人数
  area: null, // 面积
  underLightFloor: null, // 灯下层高
  tableType: null, // 摆台形式
  guildhall: '', // 会议厅名称
  guildhallPhotos: [], // 会议厅图片
  placeNum: 1, // 数量

  hasLed: false, // 是否需要led
  schemeLedNum: null, // led数量
  ledSpecs: null, // led规格说明
  schemeLedSource: null, // led规格说明

  hasTea: null, // 是否需要茶歇
  teaEachTotalPrice: null, // 茶歇标准/每人
  teaDesc: null, // 茶歇说明

  schemeUnitPlacePrice: null, // 自动测算会场单价
  schemeUnitLedPrice: null, // 自动测算led单价
  schemeUnitTeaPrice: null, // 自动测算茶歇单价

  description: null,
});

// 会议厅
const guildhallRef = ref(null);
const placeModalShow = ref<boolean>(false); // 会议厅弹窗
const guildhallIndex = ref<number>(0); // 会议厅index
const guildhallName = ref<string>(''); // 会议厅名称
const guildhallPhotoList = ref<array>([]); // 会议厅图片列表
const visible = ref<boolean>(false);
const guildhallImgs = ref<array>([]); // 会议厅图片列表

// 价格计算
const priceCalcFun = () => {
  const isAllPriceWrite = newSchemeList.value.every((e) => e.schemeUnitPlacePrice && e.schemeUnitPlacePrice > 0);
  subtotal.value = 0;

  if (isAllPriceWrite) {
    newSchemeList.value.forEach((e) => {
      if (e.placeNum !== 0) {
        subtotal.value += e.schemeUnitPlacePrice;

        if (
          e.hasLed &&
          e.schemeUnitLedPrice !== null &&
          e.schemeUnitLedPrice !== undefined &&
          e.schemeLedNum !== null &&
          e.schemeLedNum !== undefined
        ) {
          // 单价*LED数量
          subtotal.value += e.schemeUnitLedPrice * e.schemeLedNum;
        }
        if (
          e.hasTea &&
          e.teaEachTotalPrice !== null &&
          e.teaEachTotalPrice !== undefined &&
          e.schemePersonNum !== null &&
          e.schemePersonNum !== undefined
        ) {
          // 茶歇单价*会场人数
          subtotal.value += e.teaEachTotalPrice * e.schemePersonNum;
        }
      }
    });

    emit('schemePriceEmit', { type: 'place', totalPrice: subtotal.value, schemeIndex: props.schemeIndex });
  }
};

const schemePlanLabelList = [
  '酒店选择',
  '会议厅选择',
  '会议厅数量',
  '使用时间',
  '会场用途',
  '人数',
  '摆台形式',
  '面积',
  '层高',
  'LED数量',
  'LED单价',
  'LED来源',
  'LED规格描述',
  '茶歇标准',
  '茶歇说明',
  '备注',
];

// 酒店名称
const hotelNameChange = (hotelItem: HotelsArr) => {
  let str = '-';

  props.hotels.forEach((e, index) => {
    if (e.id && (e.id === hotelItem.tempSchemeHotelId || e.id === hotelItem.miceSchemeHotelId)) {
      str = `酒店${index + 1}(${e.hotelName + '/' + (hotelLevelAllConstant.ofType(e.level)?.desc || '-')})`;
    }
  });

  return str;
};

const changePrice = (index: number) => {
  // 价格计算
  priceCalcFun();
};

const addScheme = (date: string) => {
  isVerifyFailed.value = false;

  newSchemeList.value.push({
    isSchemeChangeAdd: true, // 是否变更方案新增

    ...placesParams.value,
    tempSchemeHotelId: props.hotels && props.hotels.length === 1 ? props.hotels[0].id : null,

    demandDate: date || oldSchemeList.value[0]?.demandDate || '',
  });

  schemeChangeAddFunc();

  // priceCalcFun();
};
const delScheme = (idx: number) => {
  newSchemeList.value.splice(idx, 1);

  schemeChangeDelFunc();
  // priceCalcFun();
};

// 锚点
const anchorJump = (id: string) => {
  document.getElementById(id).scrollIntoView({ behavior: 'smooth', block: 'center' });
};

// 会议厅图片查看
const guildhallImgView = (photos: array) => {
  guildhallImgs.value = photos.map((e) => {
    const obj = JSON.parse(e);

    return obj.url;
  });
  visible.value = true;
};
// 会议厅选择
const guildhallAdd = (name: string, photos: array, index: number) => {
  guildhallIndex.value = index;
  guildhallName.value = name;
  guildhallPhotoList.value = photos;

  placeModalShow.value = true;
};
const guildhallFunc = (data: PlacesArr) => {
  newSchemeList.value[guildhallIndex.value].guildhall = data.guildhallName;
  newSchemeList.value[guildhallIndex.value].guildhallPhotos =
    data.guildhallPhotos?.length > 0
      ? data.guildhallPhotos.map((e) => {
          const params = {
            name: e.name,
            url: e.filePath,
          };

          return JSON.stringify(params);
        })
      : [];
};
const placeConfirm = async () => {
  let isVerifyPassed = await guildhallRef?.value.onSubmit();

  if (isVerifyPassed) placeCancel();
};
const placeCancel = () => {
  placeModalShow.value = false;
};

// 暂存
const placeTempSave = () => {
  emit('schemePlacesEmit', {
    schemePlaces: [...newSchemeList.value],
    schemeIndex: props.schemeIndex,
  });
};

// 校验
const placeSub = () => {
  let isPlaceVerPassed = true;

  newSchemeList.value.forEach((e, i) => {
    // 只校验新增
    if (e.isSchemeChangeAdd) {
      isVerifyFailed.value = true;

      if (isPlaceVerPassed === false) return;

      if (!e.tempSchemeHotelId) {
        message.error('请选择' + e.demandDate + '会场' + (i + 1) + '酒店');

        isPlaceVerPassed = false;
        anchorJump('schemePlaceId' + e.demandDate + i);
        return;
      }

      if (!e.guildhall) {
        message.error('请填写' + e.demandDate + '会场' + (i + 1) + '会议厅名称');

        isPlaceVerPassed = false;
        anchorJump('schemePlaceId' + e.demandDate + i);
        return;
      }

      if (e.placeNum === null || e.placeNum === undefined) {
        message.error('请选择' + e.demandDate + '会场' + (i + 1) + '会议厅数量');

        isPlaceVerPassed = false;
        anchorJump('schemePlaceId' + e.demandDate + i);
        return;
      }

      if (e.usageTime === null || e.usageTime === undefined) {
        message.error('请选择' + e.demandDate + '会场' + (i + 1) + '使用时间');

        isPlaceVerPassed = false;
        anchorJump('schemePlaceId' + e.demandDate + i);
        return;
      }

      if (e.usagePurpose === null || e.usagePurpose === undefined) {
        message.error('请选择' + e.demandDate + '会场' + (i + 1) + '会场用途');

        isPlaceVerPassed = false;
        anchorJump('schemePlaceId' + e.demandDate + i);
        return;
      }

      if (e.schemePersonNum === null || e.schemePersonNum === undefined) {
        message.error('请填写' + e.demandDate + '会场' + (i + 1) + '人数');

        isPlaceVerPassed = false;
        anchorJump('schemePlaceId' + e.demandDate + i);
        return;
      }

      if (e.tableType === null || e.tableType === undefined) {
        message.error('请选择' + e.demandDate + '会场' + (i + 1) + '摆台形式');

        isPlaceVerPassed = false;
        anchorJump('schemePlaceId' + e.demandDate + i);
        return;
      }

      if (e.hasLed && (e.schemeLedNum === null || e.schemeLedNum === undefined)) {
        message.error('请填写' + e.demandDate + '会场' + (i + 1) + 'LED数量');

        isPlaceVerPassed = false;
        anchorJump('schemePlaceId' + e.demandDate + i);
        return;
      }

      if (e.hasLed && (e.schemeUnitLedPrice === null || e.schemeUnitLedPrice === undefined)) {
        message.error('请填写' + e.demandDate + '会场' + (i + 1) + 'LED单价');

        isPlaceVerPassed = false;
        anchorJump('schemePlaceId' + e.demandDate + i);
        return;
      }

      if (e.hasLed && (e.schemeLedSource === null || e.schemeLedSource === undefined)) {
        message.error('请选择' + e.demandDate + '会场' + (i + 1) + 'LED来源');

        isPlaceVerPassed = false;
        anchorJump('schemePlaceId' + e.demandDate + i);
        return;
      }

      if (e.hasLed && !e.ledSpecs) {
        message.error('请填写' + e.demandDate + '会场' + (i + 1) + 'LED规格描述');

        isPlaceVerPassed = false;
        anchorJump('schemePlaceId' + e.demandDate + i);
        return;
      }

      if (e.hasTea && (e.teaEachTotalPrice === null || e.teaEachTotalPrice === undefined)) {
        message.error('请填写' + e.demandDate + '会场' + (i + 1) + '茶歇标准');

        isPlaceVerPassed = false;
        anchorJump('schemePlaceId' + e.demandDate + i);
        return;
      }

      if (e.hasTea && !e.teaDesc) {
        message.error('请填写' + e.demandDate + '会场' + (i + 1) + '茶歇说明');

        isPlaceVerPassed = false;
        anchorJump('schemePlaceId' + e.demandDate + i);
        return;
      }

      if (e.schemeUnitPlacePrice === null || e.schemeUnitPlacePrice === undefined) {
        message.error('请填写' + e.demandDate + '会场' + (i + 1) + '单价');

        isPlaceVerPassed = false;
        anchorJump('schemePlaceId' + e.demandDate + i);
        return;
      }

      // 茶歇单价赋值
      e.schemeUnitTeaPrice = e.teaEachTotalPrice;
    }
  });

  if (isPlaceVerPassed) {
    placeTempSave();
  }

  return isPlaceVerPassed;
};

defineExpose({ placeSub, placeTempSave, addScheme });

onMounted(async () => {
  if ((props.schemeItem && props.schemeItem.places) || (props.schemeCacheItem && props.schemeCacheItem.places)) {
    oldSchemeList.value = JSON.parse(JSON.stringify(props.schemeItem))?.places || [];
    // console.log('%c [ 会场 ]-24', 'font-size:13px; background:pink; color:#bf2c9f;', oldSchemeList.value);

    if (props.isSchemeCache && props.schemeCacheItem) {
      // 缓存 - 反显
      newSchemeList.value = props.schemeCacheItem?.places || [];
    } else if (props.isSchemeBargain && props.schemeCacheItem) {
      // 议价、议价查看
      newSchemeList.value = props.schemeCacheItem?.places || [];
    } else {
      // 变更查看
      newSchemeList.value = JSON.parse(JSON.stringify(oldSchemeList.value));
    }

    // 酒店反显
    newSchemeList.value.forEach((e, index) => {
      if (props.isSchemeBargain) {
        // 议价
        e.schemeUnitPlacePrice = e.schemeUnitPlacePrice || oldSchemeList.value[index].schemeUnitPlacePrice;
        e.schemeUnitLedPrice = e.schemeUnitLedPrice || oldSchemeList.value[index].schemeUnitLedPrice;
      }

      // 原价赋值
      e.oldSchemeUnitPrice = oldSchemeList.value[index]?.schemeUnitPlacePrice || null;
      e.oldSchemeUnitLedPrice = oldSchemeList.value[index]?.schemeUnitLedPrice || null;

      if (props.hotels && props.hotels.length === 1) {
        // 是否直签酒店
        e.tempSchemeHotelId =
          props.merchantType !== 1
            ? props.hotels[0].id
            : oldSchemeList.value[index].miceSchemeHotelId || props.hotels[0].id;
      } else {
        if (e.miceSchemeHotelId) e.tempSchemeHotelId = oldSchemeList.value[index].miceSchemeHotelId;
      }

      // 会场数量 - 老数据处理
      if (e.placeNum === null) {
        e.placeNum = 1;
      }
    });

    // 价格计算
    priceCalcFun();
  }
});
</script>

<template>
  <!-- 会场方案 -->
  <div class="scheme_place" v-show="oldSchemeList.length > 0 || newSchemeList.length > 0">
    <div class="common_table">
      <!-- 左侧 -->
      <div class="common_table_l">
        <div class="scheme_plan_title" v-show="oldSchemeList.length > 0">
          <div class="scheme_plan_img mr10"></div>
          <span>会场方案</span>
        </div>

        <div class="scheme_plan_table mt20" v-for="(item, idx) in oldSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '会场' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ hotelNameChange(item) }}
                </template>
                {{ hotelNameChange(item) }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.guildhall }}
                </template>
                {{ item.guildhall }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.placeNum || '-' }}
                </template>
                {{ item.placeNum || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ PlaceUsageTimeTypeConstant.ofType(item.usageTime)?.desc || '-' }}
                </template>
                {{ PlaceUsageTimeTypeConstant.ofType(item.usageTime)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ UsagePurposeTypeConstant.ofType(item.usagePurpose)?.desc || '-' }}
                </template>
                {{ UsagePurposeTypeConstant.ofType(item.usagePurpose)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.schemePersonNum ? item.schemePersonNum + '人' : '-' }}
                </template>
                {{ item.schemePersonNum ? item.schemePersonNum + '人' : '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ TableTypeConstant.ofType(item.tableType)?.desc || '-' }}
                </template>
                {{ TableTypeConstant.ofType(item.tableType)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.area ? item.area + '㎡' : '-' }}
                </template>
                {{ item.area ? item.area + '㎡' : '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.underLightFloor ? item.underLightFloor + 'm' : '-' }}
                </template>
                {{ item.underLightFloor ? item.underLightFloor + 'm' : '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.schemeLedNum || '-' }}
                </template>
                {{ item.schemeLedNum || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.schemeUnitLedPrice || '-' }}
                </template>
                {{ item.schemeUnitLedPrice || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ LedSourceTypeConstant.ofType(item.schemeLedSource)?.desc || '-' }}
                </template>
                {{ LedSourceTypeConstant.ofType(item.schemeLedSource)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.ledSpecs || '-' }}
                </template>
                {{ item.ledSpecs || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.teaEachTotalPrice ? item.teaEachTotalPrice + '元/位' : '-' }}
                </template>
                {{ item.teaEachTotalPrice ? item.teaEachTotalPrice + '元/位' : '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.teaDesc || '-' }}
                </template>
                {{ item.teaDesc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.description || '-' }}
                </template>
                {{ item.description || '-' }}
              </a-tooltip>
            </div>
          </div>
          <div class="scheme_plan_list3 pr12">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">单价：</div>
              <div class="scheme_plan_price_value">
                {{ '¥' + formatNumberThousands(item.schemeUnitPlacePrice) }}
              </div>
            </div>
            <div class="scheme_plan_price mt36">
              <div class="scheme_plan_price_label">总额：</div>
              <div class="scheme_plan_price_value">
                {{
                  '¥' +
                  (item.schemeUnitPlacePrice
                    ? formatNumberThousands(
                        item.schemeUnitPlacePrice +
                          (item.hasLed ? item.schemeUnitLedPrice * item.schemeLedNum : 0) +
                          (item.hasTea ? item.teaEachTotalPrice * item.schemePersonNum : 0),
                      )
                    : '0.00')
                }}
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <div v-if="item.schemeUnitPlacePrice">
                <div>
                  {{ item.schemeUnitPlacePrice + '(会场价格)' }}
                </div>
                <div v-if="item.hasLed">
                  {{ item.schemeLedNum + '(数量)*' + item.schemeUnitLedPrice + '(LED单价)' }}
                </div>
                <div v-if="item.hasTea">
                  {{ item.schemePersonNum + '(人数)*' + item.teaEachTotalPrice + '(茶歇单价)' }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="common_table_divide"></div>

      <!-- 右侧 -->
      <div class="common_table_r">
        <div class="scheme_plan_title" v-show="newSchemeList.length > 0">
          <div class="scheme_plan_img mr10"></div>
          <span>会场方案</span>
        </div>

        <div class="scheme_plan_table mt20" v-for="(item, idx) in newSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '会场' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value">
              <a-tooltip
                class="pl12 pr12"
                placement="topLeft"
                v-if="
                  ['schemeBargainingEdit', 'schemeBargainingView', 'schemeChangeView'].includes(schemeChangeType) ||
                  !item.isSchemeChangeAdd
                "
              >
                <template #title>
                  {{ hotelNameChange(item) }}
                </template>
                {{ hotelNameChange(item) }}
              </a-tooltip>

              <div
                :class="['scheme_plan_border', 'p0', isVerifyFailed && !item.tempSchemeHotelId ? 'error_tip' : '']"
                v-else
              >
                <a-select
                  v-model:value="item.tempSchemeHotelId"
                  style="width: 100%"
                  :disabled="hotels && hotels.length === 1"
                  placeholder="请选择酒店"
                  :bordered="false"
                  :dropdownMatchSelectWidth="300"
                  allow-clear
                >
                  <a-select-option v-for="(item, idx) in hotels" :key="item.id" :value="item.id">
                    <a-tooltip placement="topLeft" :title="'酒店' + (idx + 1) + '-' + item.hotelName">
                      {{ '酒店' + (idx + 1) + '-' + item.hotelName }}
                    </a-tooltip>
                  </a-select-option>
                </a-select>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div :class="['scheme_plan_border', 'p0', isVerifyFailed && !item.guildhall ? 'error_tip' : '']">
                <div
                  class="pl12"
                  v-if="
                    ['schemeBargainingEdit', 'schemeBargainingView', 'schemeChangeView'].includes(schemeChangeType) ||
                    !item.isSchemeChangeAdd
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.guildhall }}
                    </template>
                    {{ item.guildhall }}
                  </a-tooltip>
                </div>
                <div v-else @click="guildhallAdd(item.guildhall, item.guildhallPhotos, idx)">
                  <a-input
                    v-model:value="item.guildhall"
                    readonly
                    style="width: calc(100% - 66px)"
                    placeholder="请填写会议厅"
                    :bordered="false"
                    :maxlength="200"
                    allow-clear
                  />
                  <div class="scheme_plan_view_img" @click.stop="guildhallImgView(item.guildhallPhotos)">
                    {{ item.guildhallPhotos && item.guildhallPhotos.length > 0 ? '查看' : '' }}
                  </div>
                  <div class="scheme_plan_edit"></div>

                  <!-- 会议厅图片预览 -->
                  <div style="display: none">
                    <a-image-preview-group :preview="{ visible, onVisibleChange: (vis) => (visible = vis) }">
                      <a-image v-for="url in guildhallImgs" :key="url" :src="url" />
                    </a-image-preview-group>
                  </div>
                </div>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && (item.placeNum === null || item.placeNum === undefined) && item.isSchemeChangeAdd
                    ? 'error_tip'
                    : '',
                ]"
              >
                <div
                  class="pl12"
                  v-if="['schemeBargainingEdit', 'schemeBargainingView', 'schemeChangeView'].includes(schemeChangeType)"
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.placeNum || '-' }}
                    </template>
                    {{ item.placeNum || '-' }}
                  </a-tooltip>
                </div>

                <a-select
                  v-else
                  v-model:value="item.placeNum"
                  @change="changePrice(idx)"
                  style="width: 100%"
                  placeholder="请选择会议厅数量"
                  :bordered="false"
                  :dropdownMatchSelectWidth="90"
                  allow-clear
                >
                  <a-select-option v-for="itemNum in [0, 1]" :key="itemNum" :value="itemNum">
                    <a-tooltip placement="topLeft" :title="itemNum">
                      {{ itemNum }}
                    </a-tooltip>
                  </a-select-option>
                </a-select>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && (item.usageTime === null || item.usageTime === undefined) ? 'error_tip' : '',
                ]"
              >
                <div
                  class="pl12"
                  v-if="
                    ['schemeBargainingEdit', 'schemeBargainingView', 'schemeChangeView'].includes(schemeChangeType) ||
                    !item.isSchemeChangeAdd
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ PlaceUsageTimeTypeConstant.ofType(item.usageTime)?.desc || '-' }}
                    </template>
                    {{ PlaceUsageTimeTypeConstant.ofType(item.usageTime)?.desc || '-' }}
                  </a-tooltip>
                </div>

                <a-select
                  v-else
                  v-model:value="item.usageTime"
                  style="width: 100%"
                  placeholder="请选择使用时间"
                  :bordered="false"
                  :dropdownMatchSelectWidth="90"
                  allow-clear
                >
                  <a-select-option
                    v-for="item in PlaceUsageTimeTypeConstant.toArray()"
                    :key="item.code"
                    :value="item.code"
                  >
                    <a-tooltip placement="topLeft" :title="item.desc">
                      {{ item.desc }}
                    </a-tooltip>
                  </a-select-option>
                </a-select>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && (item.usagePurpose === null || item.usagePurpose === undefined) ? 'error_tip' : '',
                ]"
              >
                <div
                  class="pl12"
                  v-if="
                    ['schemeBargainingEdit', 'schemeBargainingView', 'schemeChangeView'].includes(schemeChangeType) ||
                    !item.isSchemeChangeAdd
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ UsagePurposeTypeConstant.ofType(item.usagePurpose)?.desc || '-' }}
                    </template>
                    {{ UsagePurposeTypeConstant.ofType(item.usagePurpose)?.desc || '-' }}
                  </a-tooltip>
                </div>

                <a-select
                  v-else
                  v-model:value="item.usagePurpose"
                  style="width: 100%"
                  placeholder="请选择会场用途"
                  :bordered="false"
                  :dropdownMatchSelectWidth="90"
                  allow-clear
                >
                  <a-select-option
                    v-for="item in UsagePurposeTypeConstant.toArray()"
                    :key="item.code"
                    :value="item.code"
                  >
                    <a-tooltip placement="topLeft" :title="item.desc">
                      {{ item.desc }}
                    </a-tooltip>
                  </a-select-option>
                </a-select>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && (item.schemePersonNum === null || item.schemePersonNum === undefined)
                    ? 'error_tip'
                    : '',
                ]"
              >
                <div
                  class="pl12"
                  v-if="
                    ['schemeBargainingView', 'schemeChangeView', 'schemeWinBidView'].includes(schemeChangeType) ||
                    !item.isSchemeChangeAdd
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.schemePersonNum ? item.schemePersonNum + '人' : '-' }}
                    </template>
                    {{ item.schemePersonNum ? item.schemePersonNum + '人' : '-' }}
                  </a-tooltip>
                </div>

                <div class="pl12" v-else>
                  <a-input-number
                    style="width: calc(100% - 44px)"
                    v-model:value="item.schemePersonNum"
                    @blur="changePrice(idx)"
                    placeholder="请填写人数"
                    :bordered="false"
                    allow-clear
                    :min="0"
                    :max="999999"
                    :precision="0"
                  />
                  <span>人</span>
                  <div class="scheme_plan_edit"></div>
                </div>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && (item.tableType === null || item.tableType === undefined) ? 'error_tip' : '',
                ]"
              >
                <div
                  class="pl12"
                  v-if="
                    ['schemeBargainingEdit', 'schemeBargainingView', 'schemeChangeView'].includes(schemeChangeType) ||
                    !item.isSchemeChangeAdd
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ TableTypeConstant.ofType(item.tableType)?.desc || '-' }}
                    </template>
                    {{ TableTypeConstant.ofType(item.tableType)?.desc || '-' }}
                  </a-tooltip>
                </div>

                <a-select
                  v-else
                  v-model:value="item.tableType"
                  style="width: 100%"
                  placeholder="请选择摆台形式"
                  :bordered="false"
                  :dropdownMatchSelectWidth="90"
                  allow-clear
                >
                  <a-select-option v-for="item in TableTypeConstant.toArray()" :key="item.code" :value="item.code">
                    <a-tooltip placement="topLeft" :title="item.desc">
                      {{ item.desc }}
                    </a-tooltip>
                  </a-select-option>
                </a-select>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div :class="['scheme_plan_border', 'p0']">
                <div
                  class="pl12"
                  v-if="
                    ['schemeBargainingView', 'schemeChangeView', 'schemeWinBidView'].includes(schemeChangeType) ||
                    !item.isSchemeChangeAdd
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.area ? item.area + '㎡' : '-' }}
                    </template>
                    {{ item.area ? item.area + '㎡' : '-' }}
                  </a-tooltip>
                </div>

                <div class="pl12" v-else>
                  <a-input-number
                    style="width: calc(100% - 44px)"
                    v-model:value="item.area"
                    placeholder="请填写面积"
                    :bordered="false"
                    allow-clear
                    :min="0"
                    :max="999999.99"
                    :precision="2"
                  />
                  <span>㎡</span>
                  <div class="scheme_plan_edit"></div>
                </div>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div :class="['scheme_plan_border', 'p0']">
                <div
                  class="pl12"
                  v-if="
                    ['schemeBargainingView', 'schemeChangeView', 'schemeWinBidView'].includes(schemeChangeType) ||
                    !item.isSchemeChangeAdd
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.underLightFloor ? item.underLightFloor + 'm' : '-' }}
                    </template>
                    {{ item.underLightFloor ? item.underLightFloor + 'm' : '-' }}
                  </a-tooltip>
                </div>

                <div class="pl12" v-else>
                  <a-input-number
                    style="width: calc(100% - 44px)"
                    v-model:value="item.underLightFloor"
                    placeholder="请填写层高"
                    :bordered="false"
                    allow-clear
                    :min="0"
                    :max="999999.99"
                    :precision="2"
                  />
                  <span>m</span>
                  <div class="scheme_plan_edit"></div>
                </div>
              </div>
            </div>

            <div
              class="scheme_plan_value"
              v-if="['schemeChangeEdit'].includes(schemeChangeType) && item.isSchemeChangeAdd && !item.hasLed"
            >
              <div class="scheme_plan_border pl12">
                <a-button class="plan_add_btn" @click="item.hasLed = true">
                  <template #icon>
                    <div class="plan_add_img mr5"></div>
                  </template>
                  <span>添加LED</span>
                </a-button>
              </div>
            </div>
            <div class="scheme_plan_value" v-else>
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && item.hasLed && (item.schemeLedNum === null || item.schemeLedNum === undefined)
                    ? 'error_tip'
                    : '',
                ]"
              >
                <div
                  class="pl12"
                  v-if="
                    ['schemeBargainingView', 'schemeChangeView', 'schemeWinBidView'].includes(schemeChangeType) ||
                    !item.hasLed
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.schemeLedNum || '-' }}
                    </template>
                    {{ item.schemeLedNum || '-' }}
                  </a-tooltip>
                </div>

                <div class="pl12" v-else>
                  <a-input-number
                    style="width: calc(100% - 44px)"
                    v-model:value="item.schemeLedNum"
                    @blur="changePrice(idx)"
                    placeholder="LED数量"
                    :bordered="false"
                    allow-clear
                    :min="0"
                    :max="999999"
                    :precision="0"
                  />
                  <span>个</span>
                  <div class="scheme_plan_edit"></div>
                </div>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed &&
                  item.hasLed &&
                  (item.schemeUnitLedPrice === null || item.schemeUnitLedPrice === undefined)
                    ? 'error_tip'
                    : '',
                ]"
              >
                <div
                  class="pl12"
                  v-if="
                    ['schemeBargainingView', 'schemeChangeView', 'schemeWinBidView'].includes(schemeChangeType) ||
                    !item.hasLed ||
                    (!item.isSchemeChangeAdd && schemeChangeType === 'schemeChangeEdit')
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.schemeUnitLedPrice || '-' }}
                    </template>
                    {{ item.schemeUnitLedPrice || '-' }}
                  </a-tooltip>
                </div>

                <div class="pl12" v-else>
                  <a-input-number
                    style="width: calc(100% - 44px)"
                    v-model:value="item.schemeUnitLedPrice"
                    @blur="changePrice(idx)"
                    placeholder="LED单价"
                    :bordered="false"
                    allow-clear
                    :min="0"
                    :max="schemeChangeType === 'schemeBargainingEdit' ? item.oldSchemeUnitLedPrice : 99999.99"
                    :precision="2"
                  />
                  <span>元</span>
                  <div class="scheme_plan_edit"></div>
                </div>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && item.hasLed && (item.schemeLedSource === null || item.schemeLedSource === undefined)
                    ? 'error_tip'
                    : '',
                ]"
              >
                <div
                  class="pl12"
                  v-if="
                    ['schemeBargainingEdit', 'schemeBargainingView', 'schemeChangeView'].includes(schemeChangeType) ||
                    !item.hasLed ||
                    !item.isSchemeChangeAdd
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ LedSourceTypeConstant.ofType(item.schemeLedSource)?.desc || '-' }}
                    </template>
                    {{ LedSourceTypeConstant.ofType(item.schemeLedSource)?.desc || '-' }}
                  </a-tooltip>
                </div>

                <a-select
                  v-else
                  v-model:value="item.schemeLedSource"
                  style="width: 100%"
                  placeholder="请选择LED来源"
                  :bordered="false"
                  :dropdownMatchSelectWidth="90"
                  allow-clear
                >
                  <a-select-option v-for="item in LedSourceTypeConstant.toArray()" :key="item.code" :value="item.code">
                    <a-tooltip placement="topLeft" :title="item.desc">
                      {{ item.desc }}
                    </a-tooltip>
                  </a-select-option>
                </a-select>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && item.hasLed && (item.ledSpecs === null || item.ledSpecs === undefined)
                    ? 'error_tip'
                    : '',
                ]"
              >
                <div
                  class="pl12"
                  v-if="
                    ['schemeBargainingView', 'schemeChangeView', 'schemeWinBidView'].includes(schemeChangeType) ||
                    !item.hasLed ||
                    !item.isSchemeChangeAdd
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.ledSpecs || '-' }}
                    </template>
                    {{ item.ledSpecs || '-' }}
                  </a-tooltip>
                </div>

                <div v-else>
                  <a-input
                    v-model:value="item.ledSpecs"
                    style="width: calc(100% - 30px)"
                    placeholder="LED规格描述"
                    :maxlength="500"
                    :bordered="false"
                    allow-clear
                  />
                  <div class="scheme_plan_edit"></div>
                </div>
              </div>
            </div>

            <div
              class="scheme_plan_value"
              v-if="['schemeChangeEdit'].includes(schemeChangeType) && item.isSchemeChangeAdd && !item.hasTea"
            >
              <div class="scheme_plan_border pl12">
                <a-button class="plan_add_btn" @click="item.hasTea = true">
                  <template #icon>
                    <div class="plan_add_img mr5"></div>
                  </template>
                  <span>添加茶歇</span>
                </a-button>
              </div>
            </div>
            <div class="scheme_plan_value" v-else>
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed &&
                  item.hasTea &&
                  (item.teaEachTotalPrice === null || item.teaEachTotalPrice === undefined)
                    ? 'error_tip'
                    : '',
                ]"
              >
                <div
                  class="pl12"
                  v-if="
                    ['schemeBargainingView', 'schemeChangeView', 'schemeWinBidView'].includes(schemeChangeType) ||
                    !item.hasTea ||
                    (!item.isSchemeChangeAdd && schemeChangeType === 'schemeChangeEdit')
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.teaEachTotalPrice ? item.teaEachTotalPrice + '元/位' : '-' }}
                    </template>
                    {{ item.teaEachTotalPrice ? item.teaEachTotalPrice + '元/位' : '-' }}
                  </a-tooltip>
                </div>

                <div class="pl12" v-else>
                  <a-input-number
                    style="width: calc(100% - 44px)"
                    v-model:value="item.teaEachTotalPrice"
                    @blur="changePrice(idx)"
                    placeholder="请填写茶歇标准"
                    :bordered="false"
                    allow-clear
                    :min="0"
                    :max="999999.99"
                    :precision="2"
                  />
                  <span>元</span>
                  <div class="scheme_plan_edit"></div>
                </div>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && item.hasTea && (item.teaDesc === null || item.teaDesc === undefined)
                    ? 'error_tip'
                    : '',
                ]"
              >
                <div
                  class="pl12"
                  v-if="
                    ['schemeBargainingView', 'schemeChangeView', 'schemeWinBidView'].includes(schemeChangeType) ||
                    !item.hasTea ||
                    !item.isSchemeChangeAdd
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.teaDesc || '-' }}
                    </template>
                    {{ item.teaDesc || '-' }}
                  </a-tooltip>
                </div>

                <div v-else>
                  <a-input
                    v-model:value="item.teaDesc"
                    style="width: calc(100% - 30px)"
                    placeholder="请填写茶歇说明"
                    :maxlength="500"
                    :bordered="false"
                    allow-clear
                  />
                  <div class="scheme_plan_edit"></div>
                </div>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                class="pl12"
                v-if="
                  ['schemeBargainingEdit', 'schemeBargainingView', 'schemeChangeView'].includes(schemeChangeType) ||
                  !item.isSchemeChangeAdd
                "
              >
                <a-tooltip placement="topLeft">
                  <template #title>
                    {{ item.description || '-' }}
                  </template>
                  {{ item.description || '-' }}
                </a-tooltip>
              </div>

              <a-tooltip placement="topLeft" v-else>
                <template #title v-if="item.description">
                  {{ item.description || '-' }}
                </template>
                <a-input
                  v-model:value="item.description"
                  style="width: calc(100% - 30px)"
                  placeholder="备注"
                  :maxlength="500"
                  :bordered="false"
                  allow-clear
                />
                <div class="scheme_plan_edit"></div>
              </a-tooltip>
            </div>
          </div>
          <div class="scheme_plan_list3 pr12" :id="'schemePlaceId' + item.demandDate + idx">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">单价：</div>
              <div
                class="scheme_plan_price_value"
                v-if="
                  ['schemeBargainingView', 'schemeChangeView', 'schemeWinBidView'].includes(schemeChangeType) ||
                  (!item.isSchemeChangeAdd && schemeChangeType === 'schemeChangeEdit')
                "
              >
                {{ '¥' + formatNumberThousands(item.schemeUnitPlacePrice) }}
              </div>
              <div
                :class="[
                  'scheme_plan_price_value',
                  isVerifyFailed && !item.schemeUnitPlacePrice ? 'error_price_tip' : '',
                ]"
                v-else
              >
                <a-input-number
                  v-model:value="item.schemeUnitPlacePrice"
                  @change="changePrice(idx)"
                  placeholder=""
                  :bordered="false"
                  :controls="false"
                  :min="0"
                  :max="schemeChangeType === 'schemeBargainingEdit' ? item.oldSchemeUnitPrice : 999999.99"
                  :precision="2"
                  style="width: 100%"
                  allow-clear
                />
              </div>
            </div>
            <div class="scheme_plan_price mt36">
              <div class="scheme_plan_price_label">总额：</div>
              <div class="scheme_plan_price_value">
                {{
                  '¥' +
                  (item.schemeUnitPlacePrice
                    ? formatNumberThousands(
                        item.schemeUnitPlacePrice +
                          (item.hasLed ? item.schemeUnitLedPrice * item.schemeLedNum : 0) +
                          (item.hasTea ? item.teaEachTotalPrice * item.schemePersonNum : 0),
                      )
                    : '0.00')
                }}
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <div v-if="item.schemeUnitPlacePrice">
                <div>
                  {{ item.schemeUnitPlacePrice + '(会场价格)' }}
                </div>
                <div
                  v-if="
                    item.hasLed &&
                    item.schemeLedNum !== null &&
                    item.schemeLedNum !== undefined &&
                    item.schemeUnitLedPrice !== null &&
                    item.schemeUnitLedPrice !== undefined
                  "
                >
                  {{
                    item.schemeLedNum && item.schemeUnitLedPrice
                      ? item.schemeLedNum + '(数量)*' + item.schemeUnitLedPrice + '(LED单价)'
                      : ''
                  }}
                </div>
                <div
                  v-if="
                    item.hasTea &&
                    item.schemePersonNum !== null &&
                    item.schemePersonNum !== undefined &&
                    item.teaEachTotalPrice !== null &&
                    item.teaEachTotalPrice !== undefined
                  "
                >
                  {{
                    item.schemePersonNum && item.teaEachTotalPrice
                      ? item.schemePersonNum + '(人数)*' + item.teaEachTotalPrice + '(茶歇单价)'
                      : ''
                  }}
                </div>
              </div>
            </div>
            <!-- 操作 -->
            <div
              class="action_icons"
              v-if="
                ['MICE_PENDING', 'MICE_EXECUTION', 'MICE_COMPLETED'].includes(processNode) &&
                schemeChangeType === 'schemeChangeEdit' &&
                item.isSchemeChangeAdd
              "
            >
              <a-popconfirm
                :title="'确认删除会场' + (idx + 1) + '？'"
                placement="topRight"
                ok-text="确认"
                cancel-text="取消"
                @confirm="delScheme(idx)"
              >
                <div class="del_icon"></div>
              </a-popconfirm>
            </div>
          </div>
        </div>

        <div v-show="newSchemeList.length > 0" class="scheme_plan_subtotal mt16">
          {{ '小计：¥' + formatNumberThousands(subtotal) }}
        </div>

        <div
          v-if="
            ['MICE_PENDING', 'MICE_EXECUTION', 'MICE_COMPLETED'].includes(processNode) &&
            schemeChangeType === 'schemeChangeEdit'
          "
          class="add_scheme_plan mt20"
          @click="addScheme()"
        >
          <div class="plan_add_img mr8"></div>
          <span>新增会场</span>
        </div>
      </div>
    </div>

    <!-- 会议厅 - 弹窗 -->
    <a-modal
      v-model:open="placeModalShow"
      title="会议厅选择"
      width="700px"
      :keyboard="false"
      :maskClosable="false"
      :closable="false"
    >
      <div>
        <schemeGuildhall
          ref="guildhallRef"
          :guildhall="guildhallName"
          :guildhallPhotos="guildhallPhotoList"
          @guildhallFunc="guildhallFunc"
        />
      </div>
      <template #footer>
        <a-button @click="placeCancel">取消</a-button>
        <a-button type="primary" @click="placeConfirm">确定</a-button>
      </template>
    </a-modal>
  </div>
</template>

<style scoped lang="less">
.scheme_place {
  .scheme_plan_img {
    background: url('@/assets/image/demand/demand_place.png');
    background-repeat: no-repeat;
    background-size: 18px 18px;
    background-position: left center;
  }

  .scheme_plan_value {
    :deep(.ant-input-number .ant-input-number-input) {
      height: 22px;
      padding: 0;
    }

    .scheme_plan_edit {
      margin-left: 5px;
      display: inline-flex;
      vertical-align: middle;

      width: 16px;
      height: 18px;
      background: url('@/assets/image/common/edit_gray.png');
      background-repeat: no-repeat;
      background-size: 16px 16px;
    }

    .scheme_plan_view_img {
      display: inline-flex;
      width: 36px;
      height: 100%;
      text-align: center;
      color: #1868db;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .scheme_plan_price_value {
    :deep(.ant-input-number .ant-input-number-input) {
      height: 24px;
      padding: 0 5px;
      text-align: end;

      width: 84px;
      font-weight: 500;
      font-size: 14px;
      color: #1868db;
      text-align: right;
      border-bottom: 1px solid #4e5969;
    }
  }

  .error_price_tip {
    :deep(.ant-input-number .ant-input-number-input) {
      border-bottom: 2px solid #ff4d4f;
    }
  }

  .p0 {
    padding: 0 !important;
  }
  .pr0 {
    padding-right: 0 !important;
  }

  .plan_add_btn {
    /* padding: 0 20px; */
    /* width: 108px;
    height: 24px; */
    /* border: 2px solid rgba(24, 104, 219, 0.2); */
    margin-top: 2px;
    border-radius: 2px;
    display: flex;
    align-items: center;

    font-weight: 400;
    font-size: 12px;
    color: #1868db;

    .plan_add_img {
      width: 12px;
      height: 12px;
      background: url('@/assets/image/demand/demand_add_blue.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
  }

  /* custom-antd.css */
  :deep(.ant-select-disabled .ant-select-selector) {
    color: rgba(134, 144, 156, 1);
  }
}
</style>
