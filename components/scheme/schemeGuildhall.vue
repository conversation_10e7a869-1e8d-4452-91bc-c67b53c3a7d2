<script setup lang="ts">
// 会议厅选择
import { defineEmits, defineProps, onMounted, ref, reactive, defineExpose } from 'vue';
import { UploadOutlined } from '@ant-design/icons-vue';
import { fileApi } from '@haierbusiness-front/apis';
import { PlacesArr } from '@haierbusiness-front/common-libs';

const props = defineProps({
  guildhall: {
    type: String,
    default: '',
  },
  guildhallPhotos: {
    type: Array,
    default: [],
  },
});

const emit = defineEmits(['guildhallFunc']);

const schemeGuildhallFormRef = ref();
const uploadLoading = ref<boolean>(false);
const isLt50M = ref<boolean>(true);

const formState = reactive<PlacesArr>({
  guildhallName: '',
  guildhallPhotos: [],
});

// 校验
const rules = {
  guildhallName: [
    { required: true, message: '请填写会议厅名称', trigger: 'change' },
    { min: 1, max: 50, message: '长度不超过50个字符', trigger: 'blur' },
  ],
  guildhallPhotos: [
    {
      required: true,
      message: '请上传附件',
      trigger: 'blur',
    },
  ],
};

// 上传附件 - 删除
const handleRemove: UploadProps['onRemove'] = (file) => {
  const idx = formState.guildhallPhotos.findIndex((e) => e.filePath === file.filePath);

  formState.guildhallPhotos.splice(idx, 1);
};

const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  isLt50M.value = file.size / 1024 / 1024 < 50;

  if (!isLt50M.value) {
    message.error('图片最大不超过50M！');
    return Upload.LIST_IGNORE;
  }

  return isLt50M.value;
};

// 上传附件
const baseUrl = import.meta.env.VITE_BUSINESS_URL;
const uploadRequest = (options: any) => {
  uploadLoading.value = true;

  const formData = new FormData();
  formData.append('file', options.file);

  fileApi
    .upload(formData)
    .then((it) => {
      options.file.filePath = baseUrl + it.path;

      options.onProgress(100);
      options.onSuccess(it, options.file); //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

// 提交
const onSubmit = async () => {
  let isVerifyPassed = false;

  await schemeGuildhallFormRef.value
    .validate()
    .then(() => {
      emit('guildhallFunc', {
        ...formState,
      });

      isVerifyPassed = true;
    })
    .catch((err) => {
      isVerifyPassed = false;
    });

  return isVerifyPassed;
};

defineExpose({ onSubmit });

onMounted(async () => {
  formState.guildhallName = props.guildhall;
  formState.guildhallPhotos = props.guildhallPhotos.map((e) => {
    const obj = JSON.parse(e);

    return {
      name: obj.name,
      url: obj.url,
      filePath: obj.url,
    };
  });
});
</script>

<template>
  <!-- 会议厅选择 -->
  <div class="scheme_guildhall">
    <a-form
      class="mt24"
      ref="schemeGuildhallFormRef"
      :model="formState"
      :rules="rules"
      :label-col="{ style: { width: '100px' } }"
    >
      <a-row :gutter="12">
        <a-col :span="24">
          <a-form-item label="会议厅名称：" name="guildhallName">
            <a-input
              v-model:value="formState.guildhallName"
              placeholder="请填写会议厅名称"
              :maxlength="50"
              allow-clear
            />
          </a-form-item>
        </a-col>

        <a-col :span="16">
          <a-form-item label="附件：" name="guildhallPhotos">
            <a-upload
              v-model:fileList="formState.guildhallPhotos"
              :custom-request="uploadRequest"
              accept=".jpg, .png, .jpeg"
              :multiple="false"
              :max-count="99"
              :before-upload="beforeUpload"
              @remove="handleRemove"
            >
              <a-button>
                <upload-outlined></upload-outlined>
                上传会议厅图片
              </a-button>
            </a-upload>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </div>
</template>

<style scoped lang="less">
.scheme_guildhall {
}
</style>
