<script setup lang="ts">
name: 'orderList';
// 平台端-订单列表页面
import {
  ref,
  reactive,
  inject,
  onMounted,
  onUnmounted,
  defineProps,
  nextTick,
  computed,
  watch,
  onActivated,
  h,
} from 'vue';
import { DownOutlined, UploadOutlined } from '@ant-design/icons-vue';
import {
  Button,
  Dropdown,
  Menu,
  Pagination,
  BadgeRibbon,
  Tooltip,
  Popover,
  message,
  Modal,
  Badge,
} from 'ant-design-vue';
import SearchDrawer from './components/Dialog.vue';
import RefundVoucherModal from './RefundVoucherModal.vue';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import {
  SearchParams,
  MiceBidManOrderList,
  OrderListResponse,
  OrderListConstant,
  PlatformCount,
  ProcessNode,
  SelTabObj,
  TabList,
  CountSum,
  MerchantType,
  ApproveListConstant,
  SwitchStateType,
} from '@haierbusiness-front/common-libs';
import { miceBidManOrderListApi, schemeApi, portalApi, fileApi } from '@haierbusiness-front/apis';
import { useRoute, useRouter } from 'vue-router';

import { getDealTime, routerParam, getDataBy, formatNumberThousands, saveDataBy } from '@haierbusiness-front/utils';
const props = defineProps({
  type: {
    type: String,
    default: 'manage',
    // manage - 后台-订单列表
    // user - 用户端-订单列表
  },
});
const scrollPosition = ref(0);
// 状态切换对象，用于控制顶部标签页的选中状态
const selTabObj = reactive<SelTabObj>({
  selTab1: 0,
  selTab2: 0,
});
const selTab = (num: number | string, type?: string) => {
  searchParams.pageNum = 1;
  if (selTabObj[type] === num) return;
  if (type) selTabObj[type] = num;
  if (num !== 0) {
    countSum.processNodes = num;
    searchParams.processNodes = num;
    getOrderList();
  } else {
    countSum.processNodes = undefined;
    searchParams.processNodes = undefined;
    getOrderList('searchList');
  }
};
const options = ref<[]>([]);

const loading = ref(false);
const pdVerNameGet = (data: string) => {
  if (['青岛流程', '生态青岛流程'].includes(data)) return '青岛';
  if (['海尔标准流程', '生态异地流程'].includes(data)) return '异地';
  if (['会展火车票流程'].includes(data)) return '火车票';
  if (['会展机票流程'].includes(data)) return '机票';
};
// 统计总数对象，用于存储各类筛选条件的统计数量
const countSum = reactive<CountSum>({
  processNodes: undefined,
  states: [],
  keyword: undefined,
  demandItem: undefined,
  mainCode: undefined,
  miceName: undefined,
  miceType: undefined,
  operatorCode: undefined,
  operatorName: undefined,
  operatorPhone: undefined,
  consultantUserCode: undefined,
  consultantUserName: undefined,
  contactUserCode: undefined,
  contactUserName: undefined,
  contactUserPhone: undefined,
  districtType: undefined,
  startDateStart: undefined,
  startDateEnd: undefined,
  endDateStart: undefined,
  endDateEnd: undefined,
  winTheBidMerchantName: undefined,
  winTheBidMerchantCode: undefined,
  isSpecialPowers: undefined,
  isUrgent: undefined,
  isAppoint: undefined,
  demandProvince: undefined,
  demandCity: undefined,
  demandDistrict: undefined,
  demandCenterMarker: undefined,
  isConsignment: undefined,
  isPlaceOrder: undefined,
  isChange: undefined,
  isBidFailure: undefined,
  pageNum: undefined,
  pageSize: undefined,
});

// 搜索参数对象，用于存储搜索条件
const searchParams = reactive<SearchParams>({
  processNodes: undefined,
  statesName: [],
  states: [],
  keyword: undefined,
  demandItem: undefined,
  mainCode: undefined,
  miceName: undefined,
  miceType: undefined,
  operatorCode: undefined,
  operatorName: undefined,
  operatorPhone: undefined,
  consultantUserCode: undefined,
  consultantUserName: undefined,
  contactUserCode: undefined,
  contactUserName: undefined,
  contactUserPhone: undefined,
  districtType: undefined,
  pageNum: 1,
  pageSize: 10,
});
const router = useRouter();
const route = useRoute();

// 招标系统前台链接
const businessMiceBid = import.meta.env.VITE_MICE_BID_URL + '#';
// 招标系统后台台链接
const businessMiceBidMan = import.meta.env.VITE_MICE_BIDMAN_URL + '#';
// 中台首页链接
const businessIndex = import.meta.env.VITE_BUSINESS_INDEX_URL + '#';

// 费用支付
const feesPayShow = ref<boolean>(false); // 弹窗
const feesPayUrl = ref<string>(''); // 支付url
const platformFeeRate = ref<Number>(0); // 平台服务费费率
const platformFee = ref<Number>(0); // 服务费
const payWinTheBidTotalPrices = ref<Number>(0); // 中标价格

const changeSchemeRecord = ref<boolean>(false); // 弹窗
// 方案议价
const bargainingShow = ref<boolean>(false); // 弹窗
const bargainingStartShow = ref<boolean>(false); // 弹窗
const bargainingConfirmShow = ref<boolean>(false); // 弹窗
const isCanPriceChange = ref<boolean>(false); // 是否可以发起议价
const isChangeApply = ref<boolean>(false); // 是否已经发生方案变更
const priceChangeLoading = ref<boolean>(false); //
const bargainingLoading = ref<boolean>(false); // 议价
const bargainingSchemeId = ref(null); // 议价方案Id
const firstBargainingRecord = ref({}); // 议价记录第一条
const applyAttachmentObj = ref([]); // 议价资料
const priceChangeList = ref<array>([]); //
const priceChangeListScheme = ref<array>([]); //
const confirmState = ref(null); //
const confirmReason = ref<string>(''); //

const uploadLoading = ref<boolean>(false);
const isLt50M = ref<boolean>(true);
const attachmentList = ref<array>([]);
const priceChangeRemarks = ref<string>('');
const arrConfirmView = ref([
  'COST_APPROVAL',
  'MICE_PENDING',
  'MICE_EXECUTION',
  'MICE_COMPLETED',
  'BILL_CONFIRM',
  'BILL_APPROVAL',
  'BILL_RE_APPROVAL',
  'PAYMENT_CONFIRM',
  'PLATFORM_INVOICE_ENTRY',
  'VENDOR_INVOICE_ENTRY',
  'INVOICE_CONFIRM',
  'PLATFORM_REFUND_RECEIPT_UPLOAD',
  'REFUND_CONFIRM',
  'PLATFORM_PAY_RECEIPT_UPLOAD',
  'PLATFORM_INVOICE_CONFIRM',
  'SETTLEMENT_PENDING',
  'SETTLEMENT_RECORDED',
  'END',
]);
const jumpUser = (order) => {
  let url = 'https://businessmanagement-test.haier.net/hbweb/mice-support/#/index?miceId=' + order.miceId;
  window.location.href = url;
};
const specialPowersVisible = ref(false);
const specialPowersList = ref([]);
const specialPowersColumns = [
  {
    title: '权限名称',
    dataIndex: 'type',
    key: 'type',
  },
  {
    title: '权限影响范围',
    dataIndex: 'powerScope',
    key: 'powerScope',
  },
  {
    title: '操作人',
    dataIndex: 'operatorName',
    key: 'operatorName',
  },
  {
    title: '状态',
    dataIndex: 'openState',
    key: 'openState',
  },
  {
    title: '操作',
    key: 'action',
    align: 'center',
    width: 100,
    dataIndex: 'action',
  },
];
// 特殊权限列表
const specialPowers = async (order: any) => {
  currentApprovalOrder.value = order;
  const res = await miceBidManOrderListApi.powerSpecial({
    mainCode: order.mainCode,
  });
  specialPowersList.value = res;
  specialPowersVisible.value = true;
};
const powerDetailObj = reactive({});
const powerDetailVisible = ref(false);
// 权限详情
const powerDetail = async (record) => {
  Object.assign(powerDetailObj, record);
  const res = await miceBidManOrderListApi.powerView({
    id: record.id,
  });
  Object.assign(powerDetailObj, res);
  console.log(powerDetailObj);
  powerDetailVisible.value = true;
  // message.success('开通成功！');
};
// 开通权限
const powerOpen = async (record) => {
  const res = await miceBidManOrderListApi.powerOpen({
    mainCode: currentApprovalOrder.value.mainCode,
    miceName: currentApprovalOrder.value.miceName,
    type: record.type,
    powerScope: record.powerScope,
    reason: '',
  });
  if (res?.success) {
    message.success('开通成功！');
    specialPowers(currentApprovalOrder.value);
  }
};
const btnJump = async (item: MiceBidManOrderList, path?: string, type?: string) => {
  payWinTheBidTotalPrices.value = item.winTheBidTotalPrices || 0;
  platformFeeRate.value = item.platformFeeRate || 0;
  platformFee.value = item.platformFee || 0;

  let miceId = item.miceId;
  let miceDemandId = '';
  let processNode = item.processNode;
  if (item.state == 1410) processNode = 'DEMAND_PUSH';
  let status = route.query.status;
  if (item.demandId) {
    miceDemandId = item.demandId;
  }
  let pathCru = path;
  const localUrl = window.location.href;

  let record = {
    miceId: item.miceId,
    orderSource: props.type,
    orderState: item.state, // 订单驳回,110 - 查详情
    processNode: processNode,
    orderType: type === '2' ? 'detail' : '',
    finalReverseReason: [110, 120, 310, 320, 620, 910].includes(item.state) ? item.finalReverseReason : '',

    pdMainId: item.pdMainId,
    pdVerId: item.pdVerId,
  };

  if (processNode === 'DEMAND_PRE_INTERACT') {
    // 需求事先交互 - 显示价格预算
    record.previewCalc = 'previewCalc';
    record.priceSource = 'manage';
    record.demandTotalPrice = item.demandTotalPrice;
  }

  if (type === '1') {
    if (processNode === 'DEMAND_PUSH') {
      // 需求发布
      pathCru = '/bidman/publish/publishView';
    } else if (processNode === 'SCHEME_APPROVAL') {
      // 方案审核
      pathCru = '/bidman/scheme/view';
    } else if (processNode === 'BILL_CONFIRM') {
      // 账单确认
      record.hideBtn = '1';
      // 跳转需求确认页面
      const url = businessMiceBid + '/bidman/bill/confirm?record=' + routerParam(record);

      window.location.href = url;
      return;
    } else if (processNode === 'SCHEME_CONFIRM') {
      // record.miceDetails = item;
      // 方案确认
      if (props.type === 'manage') pathCru = '/bidman/scheme/confirm/view';
      else {
        // 跳转需求确认页面
        const url = businessMiceBid + '/bidman/scheme/confirm/view?record=' + routerParam(record);

        window.location.href = url;
        return;
      }
    } else if (processNode === 'BID_PUSH') {
      // 竞价推送
      pathCru = '/bidman/bid/view';
      // record.miceDetails = item;
    } else if (processNode === 'DEMAND_RECEIVE') {
      // 需求接单
      status = '1';
      pathCru = '/bidman/advisors/index';
    } else if (processNode === 'DEMAND_CONFIRM') {
      // 需求确认

      // 跳转需求确认页面
      const url = businessMiceBid + '/demand/demandConfirm?record=' + routerParam(record);

      window.location.href = url;
      return;
    } else if (processNode === 'DEMAND_SUBMIT' || processNode === 'DEMAND_PRE_INTERACT') {
      // DEMAND_SUBMIT - 需求提报
      // DEMAND_PRE_INTERACT - 需求事先交互

      if (item.demandId) record.demandId = item.demandId;

      const url = businessMiceBid + '/demand/index?record=' + routerParam(record);
      window.open(url);
      return;
    } else if (processNode === 'BID_RESULT_CONFIRM') {
      // 费用支付
      const payRes = await schemeApi.userConfirmPay(
        { miceId: miceId, callbackUrl: businessIndex + '/card-order/miceOrder' },
        (error) => {},
      );

      if (payRes && payRes.success && payRes.data) {
        feesPayUrl.value = payRes.data;
      }
      feesPayShow.value = true;

      return;
    }
  } else if (type === '2') {
    // 查看详情
    //方案确认,方案复审
    if (['SCHEME_APPROVAL', 'SCHEME_CONFIRM', 'SCHEME_RE_APPROVAL'].includes(processNode)) {
      if (props.type === 'manage') pathCru = '/bidman/scheme/confirm/view';
      else {
        // 跳转需求确认页面
        const url = businessMiceBid + '/bidman/scheme/confirm/view?record=' + routerParam(record);

        window.location.href = url;
        return;
      }
    } else if (['DEMAND_SUBMIT'].includes(processNode)) {
      message.error('需求未提报！');
      return;
    } else if (['BID_PUSH', 'BIDDING', 'BID_RESULT_CONFIRM'].concat(arrConfirmView.value).includes(processNode)) {
      if (props.type === 'manage') pathCru = '/bidman/scheme/confirm/view';
      else {
        // 跳转需求确认页面
        const url = businessMiceBid + '/bidman/scheme/confirm/view?record=' + routerParam(record);

        window.location.href = url;
        return;
      }
    } else {
      let url = '';
      if (props.type === 'manage') {
        url = businessMiceBidMan + '/bidman/orderList/meetingDetail';
      } else {
        url = businessMiceBid + '/miceOrder/meetingDetail';
      }

      // 跳转需求确认页面
      const openUrl = url + pathCru + '?record=' + routerParam(record);

      window.location.href = openUrl;
      return;
    }
  }

  router.push({
    path: pathCru,
    query: {
      record: routerParam({ ...record, orderType: type === '2' ? 'detail' : '' }),
      miceId: miceId,
      miceDemandId: miceDemandId,
      status: status,
    },
  });
};

// 订单列表数据
const orderList = ref<MiceBidManOrderList[]>([]);
// 总数据条数
const total = ref(0);

// 处理时间映射，用于显示订单处理时长
const dealTimeMap = ref<Record<string, string>>({});
let timer: number | null = null;

const getRatio = (order) => {
  if (['M0', 'M1'].includes(authority.value)) {
    if (['SCHEME_SUBMIT'].includes(order.processNode) && order.schemeWaitSubmitNum && order.schemePushNum) {
      return order.schemePushSubmitNum + '/' + (Number(order.schemePushSubmitNum) + Number(order.schemeWaitSubmitNum));
    } else if (['BIDDING'].includes(order.processNode) && order.bidWaitSubmitNum && order.bidSchemePushNum) {
      return (
        order.bidSchemePushSubmitNum + '/' + (Number(order.bidSchemePushSubmitNum) + Number(order.bidWaitSubmitNum))
      );
    }
  }
};

// 更新处理时间显示
const updateDealTimes = () => {
  orderList.value.forEach((order: MiceBidManOrderList) => {
    if (order.stateGmtModified) {
      dealTimeMap.value[order.mainCode] = getDealTime(order.stateGmtModified);
    }
  });
};

// 获取公司名称，根据屏幕宽度自动截断
const getCompany = (companyArr: array) => {
  const arr = companyArr?.filter((e) => ['1', '2'].includes(e.merchantType)) || [];
  return arr.length > 0 ? arr[0]?.merchantName : companyArr[0]?.merchantName || '-';
};
const getContact = (companyArr: array) => {
  const arr = companyArr?.filter((e) => ['1', '2'].includes(e.merchantType)) || [];
  return arr.length > 0 ? arr[0]?.contactMerchantName : companyArr[0]?.contactMerchantName || '-';
};
const judgeAuthority = (order: MiceBidManOrderList, type: string) => {
  if (order.state == 1410) return false;
  // if (order.state == 1410 && order.consultantUserCode && order.consultantUserCode === store.loginUser.username)
  //   return true;
  if (type === 'time' && ProcessNode.ofType(order.processNode)?.desc === '需求提报') return false;
  if (authority.value === 'M0' && ProcessNode.ofType(order.processNode)?.desc === '需求接单') {
    return true;
  } else if (
    order[ProcessNode.ofType(order.processNode)?.role] &&
    order[ProcessNode.ofType(order.processNode)?.role] === store.loginUser.username
  ) {
    if (ProcessNode.ofType(order.processNode)?.role === 'consultantUserCode' && props.type === 'manage') return true;
    if (ProcessNode.ofType(order.processNode)?.role === 'operatorCode' && props.type === 'user') return true;
    else return false;
  } else {
    return false;
  }
};
const authority = ref('U');
const tabList = ref<TabList[]>([]);
const store = applicationStore();
const widthSearch = ref('80%');
const getOrderList = async (type?: string, first?: boolean) => {
  loading.value = true;
  try {
    if (route.query.username) {
      searchParams.consultantUserCode = route.query.username;
    }
    if (type === 'searchList') {
      let authorArray = ['需求提报', '需求确认', '方案确认', '费用支付', '会议执行中', '账单确认'];
      if (props.type === 'manage') {
        store.loginUser.authorities.forEach((item) => {
          if (item.authority === '211') {
            // 会务顾问
            authority.value = 'M1';
            authorArray = ['需求事先交互', '需求发布', '方案审核', '竞价推送', '会议执行中', '账单复审'];
            if (first) {
              // searchParams.consultantUserName = store.loginUser.nickName;
              searchParams.consultantUserCode = store.loginUser.username;
            }
          }
          if (item.authority === '213') {
            // 会务负责人
            authority.value = 'M0';
            authorArray = ['需求接单', '需求事先交互', '需求发布', '方案审核', '竞价推送', '会议执行中', '账单复审'];
          }
        });
      }

      if (props.type === 'manage') {
        let tempStates = JSON.parse(JSON.stringify(searchParams));
        if (tempStates.states?.length > 0) {
          tempStates.states = JSON.stringify(tempStates.states).replaceAll('[', '').replaceAll(']', '');
        }
        tabList.value = await miceBidManOrderListApi.platformCount({ ...countSum, ...tempStates });
      } else {
        tabList.value = await miceBidManOrderListApi.platformCountU(countSum);
      }
      tabList.value = tabList.value.sort((a, b) => {
        return b.counts - a.counts;
      });
      tabList.value = tabList.value.filter((item) => {
        if (authorArray.includes(ProcessNode.ofType(item.processNode)?.desc)) return item;
      });
    }

    let tempStates = JSON.parse(JSON.stringify(searchParams));
    if (tempStates.states?.length > 0) {
      tempStates.states = JSON.stringify(tempStates.states).replaceAll('[', '').replaceAll(']', '');
    }
    if (props.type === 'manage') {
      // let temp = '';
      // ProcessNode.toArray().forEach((item) => {
      //   if (item?.code !== 'DEMAND_SUBMIT') temp += item.code + ',';
      // });
      // if (!tempStates.processNodes) tempStates.processNodes = temp;
      const res = (await miceBidManOrderListApi.orderList(tempStates)) as OrderListResponse;
      orderList.value = JSON.parse(JSON.stringify(res.records));
      total.value = res.total;
    } else {
      const res = (await miceBidManOrderListApi.orderListU(tempStates)) as OrderListResponse;
      orderList.value = JSON.parse(JSON.stringify(res.records));
      total.value = res.total;
    }
    orderList.value.forEach(async (item, index) => {
      if (['DEMAND_SUBMIT', 'DEMAND_PRE_INTERACT'].includes(item.processNode)) {
        const res = await getCache(item.miceId);
        if (res) {
          const resData = JSON.parse(res);

          orderList.value[index].miceName = resData.miceName;
          orderList.value[index].miceType = resData.miceType;
          orderList.value[index].personTotal = resData.personTotal;
          orderList.value[index].endDate = resData.endDate;
          orderList.value[index].startDate = resData.startDate;

          orderList.value[index].processNode = item.processNode;
        }
      }
    });
  } finally {
    loading.value = false;
  }

  let element = document.getElementById('myElement')?.offsetWidth - 40 + 'px';
  widthSearch.value = element || '80%';

  window.addEventListener('resize', function () {
    let element = document.getElementById('myElement')?.offsetWidth - 40 + 'px';
    widthSearch.value = element || '80%';
  });
};
const getCache = async (miceId: string) => {
  if (!miceId) {
    return;
  }

  const resCacheStr = await getDataBy({
    applicationCode: 'haierbusiness-mice-bid',
    cacheKey: 'haierbusiness-mice-bid_' + store.loginUser.value?.username + '_demandSubKey' + miceId, // 需求提报
  });
  const resStr = resCacheStr;

  return resStr;
};
// 复制文本到剪贴板
const getCopy = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text);
    message.success('复制成功！');
  } catch (err) {
    message.error('复制失败');
  }
};
const showTip = (order) => {
  if (order.endType == 4 && order.delayEndingDate) {
    let schemeDeadline = new Date(order.delayEndingDate);
    let curDate = new Date();
    if (curDate < schemeDeadline) {
      let remaining = getDealTime(order.delayEndingDate);

      if (remaining.length <= 8 && remaining.slice(0, 2) == '00') {
        return remaining;
      }
    }
  }
  return false;
};
const collapsed = inject('collapsed');
watch(collapsed, (newVal) => {
  if (newVal) {
    let element = document.getElementById('myElement')?.offsetWidth + 100 + 'px';
    widthSearch.value = element || '80%';
  } else {
    let element = document.getElementById('myElement')?.offsetWidth - 170 + 'px';
    widthSearch.value = element || '80%';
  }
});

// 页面加载时初始化数据
onMounted(async () => {
  getOrderList('searchList', true);
  // 启动定时器，每秒更新一次处理时间
  timer = window.setInterval(updateDealTimes, 1000);
});

onActivated(() => {
  getOrderList();
});
// 页面卸载时清理定时器
onUnmounted(() => {
  // 组件卸载时清除定时器
  if (timer) {
    clearInterval(timer);
    timer = null;
  }
});

// 搜索方法
const handleSearch = () => {
  if (searchParams.statesName.length === 0) searchParams.states = [];
  searchParams.pageNum = 1;
  getOrderList(selTabObj.selTab1 === 0 ? 'searchList' : '');
};

// 新增：让输入框的@search事件也能触发搜索
const onSearch = async () => {
  let tempStates = JSON.parse(JSON.stringify(searchParams));
  let res;
  if (props.type === 'manage') {
    res = (await miceBidManOrderListApi.orderList(tempStates)) as OrderListResponse;
  } else {
    res = (await miceBidManOrderListApi.orderListU(tempStates)) as OrderListResponse;
  }
  options.value = JSON.parse(JSON.stringify(res.records)).map((item) => {
    return { value: item.mainCode };
  });
};

// 重置搜索条件
const resetSearch1 = () => {
  (Object.keys(searchParams) as Array<keyof SearchParams>).forEach((key) => {
    if (key === 'pageNum' || key === 'pageSize') {
      searchParams[key] = key === 'pageNum' ? 1 : 10;
    } else if (key === 'statesName' || key === 'states') {
      searchParams[key] = [];
    } else {
      searchParams[key] = undefined;
    }
  });
  getOrderList('searchList');
};

const showBtn = (order) => {
  // return '账单上传'
  if ([1410].includes(order?.state)) return '重新发布';
  else if (['DEMAND_CONFIRM'].includes(order.processNode) && props.type === 'manage') return '';
  else if (['PLATFORM_INVOICE_ENTRY'].includes(order.processNode) && props.type === 'manage') return '发票上传';
  else if (['INVOICE_CONFIRM'].includes(order.processNode) && props.type === 'user') return '发票确认';
  else if (['REFUND_CONFIRM'].includes(order.processNode) && props.type === 'user') return '退款确认';
  else if (['PLATFORM_REFUND_RECEIPT_UPLOAD'].includes(order.processNode) && props.type === 'manage')
    return '上传退款凭证';
  else if (['PLATFORM_INVOICE_CONFIRM'].includes(order.processNode) && props.type === 'manage') return '发票确认';
  else if (['PLATFORM_PAY_RECEIPT_UPLOAD'].includes(order.processNode) && props.type === 'manage')
    return '上传支付凭证';
  else return ProcessNode.ofType(order.processNode)?.desc;
};

// 动态按钮点击事件处理
const handleDynamicBtnClick = (order) => {
  const btnText = showBtn(order);

  switch (btnText) {
    case '发票上传':
      showInvoiceUploader(order);
      break;
    case '发票确认':
      // 这里可以添加发票确认的逻辑
      invoiceConfirmation(order, 1);
      break;
    case '退款确认':
      // 这里可以添加退款确认的逻辑
      invoiceConfirmation(order, 2);
      break;
    case '上传退款凭证':
      showRefundVoucherModal(order);
      break;
    case '上传支付凭证':
      showPaymentVoucherModal(order);
      break;
    case '账单复审':
      billConfirm(order);
      break;
    case '重新发布':
      // 重新发布逻辑
      btnJump(order, '', '1');
      break;
    default:
      // 默认使用原来的 btnJump 逻辑
      btnJump(order, '', '1');
      break;
  }
};
// 显示高级搜索抽屉
const searchDrawer = ref();
const showDrawer = () => {
  searchDrawer.value.show({
    // consultantUserName: searchParams.consultantUserName,
    consultantUserCode: searchParams.consultantUserCode,
    states: searchParams.states,
    keyword: searchParams.keyword,
  });
};

// 处理高级搜索
const handleAdvancedSearch = (formData: SearchParams) => {
  Object.assign(searchParams, formData);
  searchParams.pageNum = 1;
  getOrderList(selTabObj.selTab1 === 0 ? 'searchList' : '');
};

// 处理重置搜索
const handleResetSearch = (formData: SearchParams) => {
  Object.assign(searchParams, formData);
  getOrderList(selTabObj.selTab1 === 0 ? 'searchList' : '');
};

// 处理分页变化
const handlePageChange = (page: number, pageSize: number) => {
  searchParams.pageNum = page;
  searchParams.pageSize = pageSize;
  getOrderList();
};

// 审批列表弹窗相关
const approvalModalVisible = ref(false);
const currentApprovalOrder = ref(null);
const approvalList = ref([]);

// 凭证弹框相关
const voucherModalVisible = ref(false);
const currentVoucherOrder = ref(null);
const voucherType = ref<'refund' | 'payment'>('refund');

// 表格列定义
const columns = [
  {
    title: '名称',
    dataIndex: 'reason',
    key: 'reason',
  },
  {
    title: '状态',
    dataIndex: 'state',
    key: 'state',
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    key: 'gmtCreate',
  },
  {
    title: '完成时间',
    dataIndex: 'gmtModified',
    key: 'gmtModified',
  },
  {
    title: '操作',
    key: 'action',
    width: 100,
    dataIndex: 'action',
  },
];
const mainId = ref('');
const orderCopy = (order) => {
  let loadingM = false;
  Modal.confirm({
    title: '复制订单',
    content: '是否确认复制订单？',
    okButtonProps: {
      loading: loadingM,
    },
    async onOk() {
      loadingM = true;
      let resDemand = await miceBidManOrderListApi.platformDetails({
        miceId: order.miceId,
        miceDemandId: order.miceDemandId,
      });
      let params = {
        ...resDemand,
        demandType: props.type === 'manage' ? 4 : 1, // 1, "用户提报版本"   4, "顾问代提版本"
      };
      const res = await portalApi.createOrder(params);

      if (res?.success && res?.data.mainId) {
        const record = {
          miceId: res?.data.mainId,
          mainCode: res?.data.mainCode || '',
          pdMainId: resDemand.pdMainId,
        };

        // 后端缓存
        await saveDataBy({
          applicationCode: 'haierbusiness-mice-bid',
          // 规则: haierbusiness-mice-bid_工号_你业务的缓存key
          cacheKey: 'haierbusiness-mice-bid_' + store.loginUser.username + '_demandSubKey' + record.miceId, // 需求提报
          cacheValue: JSON.stringify({
            ...params,
            ...record,
          }),
        });

        // 需求提报
        const openUrl = businessMiceBid + '/demand/index?record=' + routerParam(record);
        window.open(openUrl);
      }
      loadingM = false;
      // if (res2.success) {
      //   message.success('复制成功！');
      //   getOrderList();
      // }
    },
    onCancel() {
      console.log('Cancel');
    },
    class: 'test',
  });
};
// 显示审批列表弹窗
const showApprovalModal = (order) => {
  currentApprovalOrder.value = order;
  approvalModalVisible.value = true;
  mainId.value = order.miceId;
  getApprovalList();
};
// 显示发票上传弹窗
const showInvoiceUploader = (order) => {
  router.push({
    path: '/bidman/invoiceUploader/index',
    query: {
      record: routerParam(order),
    },
  });
};
// 跳转发票确认页面
const invoiceConfirmation = (order, num: number) => {
  router.push({
    path: '/card-order/invoiceConfirm',
    query: {
      record: routerParam(order),
      type: num,
    },
  });
};

// 显示凭证上传弹窗
const showVoucherModal = (order, type: 'refund' | 'payment') => {
  currentVoucherOrder.value = order;
  voucherType.value = type;
  voucherModalVisible.value = true;
};

// 显示退款凭证上传弹窗（保持向后兼容）
const showRefundVoucherModal = (order) => {
  showVoucherModal(order, 'refund');
};

// 显示支付凭证上传弹窗
const showPaymentVoucherModal = (order) => {
  showVoucherModal(order, 'payment');
};

// 凭证上传成功回调
const handleVoucherSuccess = () => {
  // 可以在这里刷新订单列表或显示成功提示
  getOrderList();
};

const finishRemark = ref('');
const loadingModal = ref(false);
const finishModal = ref(false);
const currentOrder = reactive({});
const onOk = async () => {
  loadingModal.value = true;
  if (finishRemark.value === '') {
    message.error('原因不能为空！');
    return;
  }
  let res = await miceBidManOrderListApi.submitFinish({
    miceId: currentOrder.miceId,
    finishRemark: finishRemark.value,
  });
  if (res.success) {
    finishRemark.value = '';
    message.success('结束成功！');
    finishModal.value = false;
    getOrderList();
  }
  loadingModal.value = false;
};
// 切换中标方案记录
let priceChangeColumnsScheme = [
  {
    title: '切换前服务商',
    dataIndex: 'merchantNameOriginal',
    width: 280,
    ellipsis: true,
    align: 'center',
  },
  {
    title: '切换后服务商',
    dataIndex: 'merchantNameNew',
    width: 280,
    ellipsis: true,
    align: 'center',
  },
  {
    title: '审批状态',
    dataIndex: 'switchStateName',
    // width: 160,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '变更说明',
    dataIndex: 'reason',
    // width: 160,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '附件',
    dataIndex: 'doc',
    align: 'center',
    // ellipsis: true,
  },
];
// 议价记录
let priceChangeColumns = [
  {
    title: '序号',
    dataIndex: '1',
    width: 50,
    ellipsis: true,
    align: 'center',
    customRender: ({ index }) => {
      return index + 1;
    },
  },
  {
    title: '商户名称',
    dataIndex: 'dataIndex2',
    width: 280,
    ellipsis: true,
    align: 'center',
    customRender: ({ record }) => {
      return record.merchantName || '-';
    },
  },
  {
    title: '议价状态',
    dataIndex: 'dataIndex3',
    width: 130,
    ellipsis: true,
    align: 'center',

    customRender: ({ record }) => {
      const stateName = {
        10: '顾问待确认', // 发起议价确认中
        20: '顾问驳回', // 发起议价驳回
        30: '待服务商议价', // 议价提报中
        40: '用户待确认', // 议价确认中
        50: '议价完成',
        60: '议价驳回',
      };

      return stateName[record.state] || '-';
    },
  },
  {
    title: '变更前价格',
    dataIndex: 'dataIndex3',
    width: 110,
    ellipsis: true,
    align: 'center',

    customRender: ({ record }) => {
      return record.changeBeforePrice || '-';
    },
  },
  {
    title: '变更后价格',
    dataIndex: 'dataIndex4',
    width: 110,
    ellipsis: true,
    align: 'center',

    customRender: ({ record }) => {
      return record.changeAfterPrice || '-';
    },
  },
  {
    title: '议价原因',
    dataIndex: '5',
    width: 180,
    align: 'center',
    ellipsis: true,
    customRender: ({ record }) => {
      return record.reason || '-';
    },
  },
  {
    title: '驳回原因',
    dataIndex: '5',
    // width: 160,
    align: 'center',
    ellipsis: true,
    customRender: ({ record }) => {
      return record.rejectReason || '-';
    },
  },
  {
    title: '操作',
    dataIndex: '6',
    width: 100,
    align: 'center',
    ellipsis: true,
    customRender: ({ record }) => {
      return authority.value === 'M1' && [10].includes(record.state)
        ? h(
            'a',
            {
              style: {
                color: '#1868DB',
              },
              onClick: () => {
                schemeBargainingBtn(record, 'bargainingConfirm');
              },
            },
            '议价确认',
          )
        : '-';
    },
  },
];
// 方案议价
const schemeBargainingBtn = async (order, type) => {
  priceChangeList.value = [];

  if (type === 'bargaining') {
    // 方案议价
    isCanPriceChange.value = false;
    bargainingShow.value = true;
  } else {
    // 议价确认
    bargainingConfirmShow.value = true;
  }

  bargainingLoading.value = true;
  // 方案详情
  let res = [];
  if (props.type === 'manage') {
    res = await schemeApi.getSchemeDetailsByPlatform({
      miceId: order.miceId,
      schemeTypes: '2',
    });
  } else {
    res = await schemeApi.getSchemeDetailsByUser({
      miceId: order.miceId,
      schemeTypes: '2',
    });
  }

  // 筛选旅行社or酒店服务商
  const filterMerchantList = res.filter((e) => e.merchantType === 1 || e.merchantType === 2);

  bargainingSchemeId.value = filterMerchantList[0]?.id || null;

  let res1 = [];
  if (props.type === 'manage') {
    res1 = await schemeApi.platformPriceChangeRecord({
      miceId: order.miceId,
    });
  } else {
    res1 = await schemeApi.getSchemePriceChangeRecordByUser({
      miceId: order.miceId,
    });
  }

  priceChangeList.value = res1 || [];

  firstBargainingRecord.value = res1[0] || {};
  applyAttachmentObj.value = firstBargainingRecord.value.applyAttachment?.map((e) => {
    return JSON.parse(e);
  });

  if (type === 'bargaining') {
    // 10: '发起议价确认中',
    // 20: '发起议价驳回',
    // 30: '议价提报中',
    // 40: '议价确认中',
    // 50: '议价完成',
    // 60: '议价驳回',
    // 或者无议价记录
    isCanPriceChange.value =
      ([20, 50, 60].includes(firstBargainingRecord.value?.state) || priceChangeList.value.length === 0) &&
      !order.isChangeApply;

    isChangeApply.value = order.isChangeApply;
  } else {
    // 议价确认
    confirmState.value = null;
    confirmReason.value = '';

    closeBargainingModal();
  }
  bargainingLoading.value = false;
};
const closeBargainingModal = () => {
  bargainingShow.value = false;
  changeSchemeRecord.value = false;
  bargainingStartShow.value = false;
};

const closeSchemeRecordModal = () => {
  bargainingShow.value = false;
  closeBargainingStartModal();
};

// 发起议价
const bargainingBtn = () => {
  if (isChangeApply.value) {
    Modal.warning({
      title: '方案已发生变更，不允许议价！',
      okText: '确定',
    });
    return;
  }

  if (![20, 50, 60].includes(firstBargainingRecord.value?.state) && priceChangeList.value.length > 0) {
    Modal.warning({
      title: '当前会议议价中',
      okText: '确定',
    });
    return;
  }

  bargainingStartShow.value = true;

  attachmentList.value = [];

  priceChangeRemarks.value = '';
};
const bargainingStartOk = async () => {
  // if (!attachmentList.value || attachmentList.value.length === 0) {
  //   message.error('请上传议价资料！');

  //   return;
  // }
  if (!priceChangeRemarks.value) {
    message.error('请填写议价原因！');

    return;
  }

  const fileList = attachmentList.value.map((e) => {
    const obj = { name: e.name, url: e.filePath };
    return JSON.stringify(obj);
  });

  priceChangeLoading.value = true;

  (props.type === 'manage' ? schemeApi.schemePriceChangeByPlatform : schemeApi.schemePriceChangeByUser)({
    schemeId: bargainingSchemeId.value,
    applyAttachment: fileList,
    reason: priceChangeRemarks.value,
  })
    .then((res) => {
      message.success('提交成功！');

      closeBargainingModal();
      handleSearch();
    })
    .finally(() => {
      priceChangeLoading.value = false;
    });
};
const closeBargainingStartModal = () => {
  bargainingStartShow.value = false;
};
const bargainingConfirmOk = async () => {
  if (!confirmState.value) {
    message.error('请选择是否通过议价！');

    return;
  }
  if (confirmState.value === 2 && !confirmReason.value) {
    message.error('请填写驳回原因！');

    return;
  }

  priceChangeLoading.value = true;

  schemeApi
    .schemePriceChangeConfirmByPlatform({
      id: firstBargainingRecord.value.id,
      // miceOriginalSchemeId: bargainingSchemeId.value,
      confirmState: confirmState.value,
      reason: confirmReason.value,
    })
    .then((res) => {
      message.success('操作成功！');

      closeBargainingConfirmModal();
      handleSearch();
    })
    .finally(() => {
      priceChangeLoading.value = false;
    });
};
const closeBargainingConfirmModal = () => {
  bargainingConfirmShow.value = false;
};
const fileClick = (url: string) => {
  window.open(url);
};

// 上传 - 删除
const handleRemove: UploadProps['onRemove'] = (file) => {};
const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  isLt50M.value = file.size / 1024 / 1024 < 50;

  if (!isLt50M.value) {
    message.error('文件最大不超过50M！');
    return Upload.LIST_IGNORE;
  }

  return isLt50M.value;
};
// 上传附件
const baseUrl = import.meta.env.VITE_BUSINESS_URL;
const uploadRequest = (options: any) => {
  uploadLoading.value = true;

  const formData = new FormData();
  formData.append('file', options.file);

  fileApi
    .upload(formData)
    .then((it) => {
      options.file.filePath = baseUrl + it.path;

      options.onProgress(100);
      options.onSuccess(it, options.file); //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};
const presentConfirm = async (order) => {
  // 跳转需求确认页面
  const url = businessMiceBid + '/bidman/present/confirm?record=' + routerParam({ ...order, hideBtn: '1' });
  window.location.href = url;
};

// 账单复核
const billConfirm = async (order) => {
  // 跳转账单复核页面
  const url = businessMiceBidMan + '/bidman/billReview/index?record=' + routerParam({ ...order });
  window.location.href = url;
};
const takeDelivery = async (order) => {
  let loadingM = false;
  Modal.confirm({
    title: '确认收货',
    content: '是否确认收货？',
    okButtonProps: {
      loading: loadingM,
    },
    async onOk() {
      const res = await schemeApi.confirmReceive({ mainCode: order.mainCode });
      if (res.success) {
        message.success('确认成功！');
        getOrderList();
      }
      loadingM = false;
    },
    onCancel() {
      console.log('Cancel');
    },
    class: 'test',
  });
};
const overProcess = async (order) => {
  Object.assign(currentOrder, order);
  finishModal.value = true;
};
const cancelSchemeModal = ref(false);
const cancelSchemeRemark = ref('');

const cancelScheme = async () => {
  loadingModal.value = true;
  if (cancelSchemeRemark.value == '') {
    message.error('请输入撤回原因！');
    return;
  }
  const res = await schemeApi.againPush({ miceId: currentOrder.miceId, backReason: cancelSchemeRemark.value });
  if (res.success) {
    message.success('撤回成功！');
    cancelSchemeModal.value = false;
    cancelSchemeRemark.value = '';
    getOrderList();
  }
  loadingModal.value = false;
};
const cancelProcess = async (order) => {
  let loadingM = false;
  Modal.confirm({
    title: '取消自动结束提报',
    content: '是否确认取消自动结束提报？',
    okButtonProps: {
      loading: loadingM,
    },
    async onOk() {
      loadingM = true;
      const res = await schemeApi.delayFinish({ miceId: order.miceId });
      if (res.success) {
        message.success('取消成功！');
        getOrderList();
      }
      loadingM = false;
    },
    onCancel() {
      console.log('Cancel');
    },
    class: 'test',
  });
};
const showPush = async (order) => {
  let resDemand = await miceBidManOrderListApi.platformDetails({
    miceId: order.miceId,
  });
  let hotelIdList = [];
  resDemand.hotels.forEach((item) => {
    hotelIdList.push(item.id);
  });
  if (props.type === 'manage')
    router.push({
      path: '/bidman/scheme/publish/view',
      query: {
        record: JSON.stringify({
          miceId: order.miceId,
          miceDemandId: order.demandId,
          status: 'show',
          hotels: hotelIdList,
          type: 'show',
        }),
      },
    });
  else {
    // 跳转需求确认页面
    const url =
      businessMiceBid +
      '/bidman/scheme/publish/view?record=' +
      routerParam({
        miceId: order.miceId,
        miceDemandId: order.demandId,
        status: 'show',
        hotels: hotelIdList,
        type: 'show',
      });
    window.location.href = url;
    return;
  }
};
const showBidPushed = ref(false);

const bidedColumn = ref([
  {
    title: '服务商类型',
    dataIndex: 'type',
    key: 'type',
    customCell: (record, rowIndex, column) => {
      return { rowSpan: record.rowSpan };
    },
  },
  {
    title: '标的数量',
    dataIndex: 'num',
    key: 'num',
    customCell: (record, rowIndex, column) => {
      return { rowSpan: record.rowSpan };
    },
  },
  { title: '方案名称', dataIndex: 'schemeName', key: 'schemeName' },
  { title: '参与服务商', dataIndex: 'scheme', key: 'scheme' },
  {
    title: '竞价截止时间',
    dataIndex: 'biddingDeadline',
    key: 'biddingDeadline',
    customCell: (record, rowIndex, column) => {
      return { rowSpan: record.rowSpan };
    },
  },
]);
const bidedData = ref([]);

const showBid = async (order) => {
  let pushedData = await schemeApi.pushedDetails({
    miceId: order.miceId,
  });
  bidedData.value = [];
  let num = 0;
  pushedData.merchantTypes.forEach((item, index) => {
    item.schemes.forEach((item1, index1) => {
      num++;
      let arr = [];
      item1.merchants.forEach((item2) => {
        let tempItem = item2;
        tempItem.bidMerchantName = item2.bidMerchantName;
        tempItem.schemeName = `方案${num}`;
        arr.push(item2);
      });
      bidedData.value.push({
        merchantType: item.merchantType,
        rowSpan: item.schemes.length == 1 ? 1 : index1 === 0 ? item.schemes.length : 0,
        type: MerchantType.ofType(item.merchantType)?.desc,
        num: item.bidNum,
        schemeName: `方案${num}`,
        biddingDeadline: pushedData.biddingDeadline,
        scheme: arr,
      });
    });
  });
  showBidPushed.value = true;
};
const getApprovalList = async () => {
  const res = await miceBidManOrderListApi.recordPage({
    mainId: mainId.value,
    pageNum: 1,
    pageSize: 999,
    category: 'PROCESS',
  });

  approvalList.value = res.records;
};
// 关闭审批列表弹窗
const closeApprovalModal = () => {
  approvalModalVisible.value = false;
  feesPayShow.value = false;
};

const PayHandleOk = () => {
  window.open(feesPayUrl.value);
  closeApprovalModal();
};

// 查看审批详情
const viewApprovalDetail = (item) => {
  window.location.href = `${import.meta.env.VITE_BUSINESS_PROCESS_URL}?code=${item.approveCode}#/details`;
};
const handleStateChange = (values: string[]) => {
  searchParams.states = [];
  searchParams.statesName = values;
  OrderListConstant.toArray().forEach((item) => {
    if (values.includes(item.desc)) searchParams.states.push(item.code);
  });
};
// 撤销审批
const cancelApproval = async (item) => {
  const res = await miceBidManOrderListApi.recordRevoke({
    processCode: item.approveCode,
  });
  getApprovalList();
  getOrderList();
  message.success('撤销审批成功');
};

const switchBidSchemeModal = ref(false);
const switchBidSchemeReason = ref('');
const switchBidSchemeAttachment = ref([]);
const switchBidSchemeTableData = ref();
const selectedSchemeId = ref(null);
const switchBidLoading = ref(false);
const businessProcess = import.meta.env.VITE_BUSINESS_PROCESS_URL;
const approvalModalShow = ref(false);
const approveCode = ref<string>(''); // 审批流Code
const publishModalShow = ref(false);
const getOrderGoing = async () => {
  let arr = [false, false, false, false];
  let order = currentApprovalOrder.value;
  try {
    // 方案议价
    let res1 = [];
    if (props.type === 'manage') {
      res1 = await schemeApi.platformPriceChangeRecord({
        miceId: order.miceId,
      });
    } else {
      res1 = await schemeApi.getSchemePriceChangeRecordByUser({
        miceId: order.miceId,
      });
    }
    priceChangeList.value = res1 || [];
    firstBargainingRecord.value = res1[0] || {};
    applyAttachmentObj.value = firstBargainingRecord.value.applyAttachment?.map((e) => {
      return JSON.parse(e);
    });
    if (
      ([20, 50, 60].includes(firstBargainingRecord.value?.state) || priceChangeList.value.length === 0) &&
      !order.isChangeApply
    )
      arr[1] = true;
    // 切换中标方案
    let res = await schemeApi.switchRecord({
      miceId: order.miceId,
    });
    let resTemp = res.filter((item) => item.switchState == 10);
    if (resTemp.length == 0) {
      arr[0] = true;
    }
    // 账单确认

    // 方案变更
  } finally {
  }
};
const showSwitchBidSchemeModal = async (order) => {
  currentApprovalOrder.value = order;
  let res = await schemeApi.switchRecord({
    miceId: order.miceId,
  });
  if (res.length === 0) {
    changeScheme();
  } else {
    priceChangeListScheme.value = res.map((item) => {
      if (item.switchState) item.switchStateName = SwitchStateType.ofType(item.switchState)?.desc;
      if (item.originalMerchants)
        item.merchantNameOriginal =
          `<div>` + item.originalMerchants.map((item1) => item1.merchantName).join('</div>') + `</div>`;
      else item.merchantNameOriginal = '-';
      if (item.newMerchants)
        item.merchantNameNew = `<div>` + item.newMerchants.map((item1) => item1.merchantName).join('</div>') + `</div>`;
      else item.merchantNameNew = '-';
      let str = '';
      let document = {};
      item.schemeBidSwitchPaths.forEach((item1, index) => {
        try {
          document = JSON.parse(item1);
        } catch (error) {
          console.log(error);
        }

        str += `<a target='_blank' href='${document.url}'>${
          document.name
        }</a><span style='margin-right: 10px;color: #86909c' >${
          index == item.schemeBidSwitchPaths?.length - 1 ? '' : ','
        }</span>`;
      });
      item.doc = str;
      return item;
    });
    changeSchemeRecord.value = true;
  }

  let resTemp = res.filter((item) => item.switchState == 10);
  if (resTemp.length != 0) {
    // message.error('切换中标方案审批中！');
    isCanPriceChange.value = false;
    return;
  } else {
    isCanPriceChange.value = true;
  }
};
const changeScheme = async () => {
  loadingModal.value = true;
  try {
    changeSchemeRecord.value = false;
    let resScheme = await schemeApi.schemePlatformDetails({
      miceId: currentApprovalOrder.value.miceId,
      isBackup: true,
    });
    switchBidSchemeTableData.value = JSON.parse(JSON.stringify(resScheme)).filter((item) => item.winState != 0);

    switchBidSchemeTableData.value.sort((a, b) => {
      return a.schemeCombinationTotalPrice - b.schemeCombinationTotalPrice;
    });
  } catch {
    // console.log(resScheme);
  } finally {
    if (switchBidSchemeTableData.value.length == 0) {
      message.error('无可切换的中标方案！');
      return;
    } else switchBidSchemeModal.value = true;
    loadingModal.value = false;
  }
};
const handleRemoveScheme = (file: any) => {
  const index = switchBidSchemeAttachment.value.findIndex((item) => item.uid === file.uid);
  if (index > -1) {
    switchBidSchemeAttachment.value.splice(index, 1);
  }
};
const handleSwitchBidSchemeCancel = () => {
  switchBidSchemeModal.value = false;
  currentApprovalOrder.value = {};
  switchBidSchemeReason.value = '';
  selectedSchemeId.value = null;
  handleRemoveScheme();
};
const handleSwitchBidSchemeOk = async () => {
  try {
    loadingModal.value = true;
    switchBidLoading.value = true;
    if (switchBidSchemeReason.value === '') {
      message.error('切换原因不能为空！');
      return;
    }

    if (['', null].includes(selectedSchemeId.value)) {
      message.error('请选择方案！');
      return;
    }
    if (switchBidSchemeAttachment.value.length == 0) {
      message.error('请上传文件说明！');
      return;
    }
    let res = await miceBidManOrderListApi.bidSwitch({
      miceId: currentApprovalOrder.value.miceId,
      schemeCombinationId: selectedSchemeId.value,
      switchReason: switchBidSchemeReason.value,
      schemeBidSwitchPaths: switchBidSchemeAttachment.value.map((item) => {
        return JSON.stringify({
          name: item.name,
          url: item.filePath,
        });
      }),
    });
    if (res.success) {
      message.success('提交成功！');
      approveCode.value = res.data.processCode;
      approvalModalShow.value = true;

      // handleSwitchBidSchemeCancel();
    }
  } finally {
    switchBidLoading.value = false;
    loadingModal.value = false;
  }
};

// 三击复制id事件
const clickCount = ref(0);
const clickTimer = ref(null);

const handleOrderNoClick = (miceId) => {
  clickCount.value++;

  if (clickCount.value === 3) {
    getCopy(miceId);
    clickCount.value = 0;
    if (clickTimer.value) {
      clearTimeout(clickTimer.value);
      clickTimer.value = null;
    }
  } else {
    if (clickTimer.value) {
      clearTimeout(clickTimer.value);
    }

    clickTimer.value = setTimeout(() => {
      clickCount.value = 0;
    }, 500); // 500ms内需要完成三次点击才算三击
  }
};
const getSerchItem = computed(() => {
  if (searchDrawer.value?.tempFormState) {
    let tempObj = searchDrawer.value.tempFormState;
    Object.assign(tempObj, {
      consultantUserCode: searchParams.consultantUserCode,
    });
    for (let key in tempObj) {
      if (tempObj[key]?.length > 0) return true;
    }
  }
  return false;
});
onUnmounted(() => {
  // 组件卸载时清除定时器
  if (timer) {
    clearInterval(timer);
    timer = null;
  }
  if (clickTimer.value) {
    clearTimeout(clickTimer.value);
    clickTimer.value = null;
  }
});
</script>

<template>
  <a-spin :spinning="loading">
    <div class="container" id="myElement">
      <!-- 顶部区域：包含状态栏和搜索框 -->
      <a-affix :offset-top="0" v-if="props.type === 'user'">
        <div class="top-section" v-show="!route.query.username" :style="`width:${widthSearch}`">
          <div class="header">
            <!-- 状态切换栏：全部订单、待处理、进行中、待结算 -->
            <div class="status-bar">
              <div
                style="padding-right: 16px"
                :class="'status-item ' + (selTabObj.selTab1 === 0 ? 'active' : '')"
                @click="selTab(0, 'selTab1')"
              >
                <span>全部订单</span>
              </div>
              <div
                v-for="(item, index) in tabList"
                :key="item.processNode"
                :class="'status-item ' + (selTabObj.selTab1 === item.processNode ? 'active' : '')"
                @click="selTab(item.processNode, 'selTab1')"
              >
                <span>{{ ProcessNode.ofType(item.processNode).desc }}</span>
                <span class="order-count">{{ item.counts }}</span>
              </div>
            </div>

            <!-- 搜索区域：会议状态选择和关键词搜索 -->
            <div class="search-input">
              <!-- <div class="search-label">会议状态:</div> -->
              <!-- <a-select
            :dropdownStyle="{ minWidth: '280px' }"
            v-model:value="searchParams.statesName"
            mode="multiple"
            :options="OrderListConstant.toArray()"
            :field-names="{ label: 'desc', value: 'desc' }"
            @change="handleStateChange"
            size="small"
            allowClear
          >
          </a-select> -->
              <!-- <a-auto-complete
                size="small"
                v-model:value="searchParams.keyword"
                :options="options"
                placeholder="单号/名称/经办人/地点搜索"
                @select="
                  () => {
                    options.value = [];
                  }
                "
                @search="onSearch"
                allowClear
              /> -->
              <a-input
                size="small"
                type="text"
                v-model:value="searchParams.keyword"
                placeholder="单号/名称/经办人/地点搜索"
                allowClear
              />
              <Button class="primary1" type="primary" @click="handleSearch">搜索</Button>
              <a-badge dot v-if="getSerchItem">
                <Button class="plain1" @click="showDrawer">高级搜索</Button>
              </a-badge>
              <Button v-else class="plain1" @click="showDrawer">高级搜索</Button>
              <search-drawer
                v-if="showDrawer"
                ref="searchDrawer"
                :user1="{
                  consultantUserCode: searchParams.consultantUserCode,
                  states: searchParams.states,
                  keyword: searchParams.keyword,
                }"
                @search="handleAdvancedSearch"
                @resetSearch="handleResetSearch"
              />
            </div>
          </div>

          <!-- 过滤标签：接单、需求互动等业务流程标签 -->
          <div class="filter-tags"></div>
        </div>
      </a-affix>
      <div v-else class="top-section" v-show="!route.query.username" :style="`width:${widthSearch}`">
        <div class="header">
          <!-- 状态切换栏：全部订单、待处理、进行中、待结算 -->
          <div class="status-bar">
            <div
              style="padding-right: 16px"
              :class="'status-item ' + (selTabObj.selTab1 === 0 ? 'active' : '')"
              @click="selTab(0, 'selTab1')"
            >
              <span>全部订单</span>
            </div>
            <div
              v-for="(item, index) in tabList"
              :key="item.processNode"
              :class="'status-item ' + (selTabObj.selTab1 === item.processNode ? 'active' : '')"
              @click="selTab(item.processNode, 'selTab1')"
            >
              <span>{{ ProcessNode.ofType(item.processNode).desc }}</span>
              <span class="order-count">{{ item.counts }}</span>
            </div>
          </div>

          <!-- 搜索区域：会议状态选择和关键词搜索 -->
          <div class="search-input">
            <a-input
              size="small"
              type="text"
              v-model:value="searchParams.keyword"
              placeholder="单号/名称/经办人/地点搜索"
              allowClear
            />
            <Button class="primary1" type="primary" @click="handleSearch">搜索</Button>
            <a-badge dot v-if="getSerchItem">
              <Button class="plain1" @click="showDrawer">高级搜索</Button>
            </a-badge>
            <Button v-else class="plain1" @click="showDrawer">高级搜索</Button>
            <search-drawer
              ref="searchDrawer"
              :user1="{
                consultantUserCode: searchParams.consultantUserCode,
                states: searchParams.states,
                keyword: searchParams.keyword,
              }"
              @search="handleAdvancedSearch"
              @resetSearch="handleResetSearch"
            />
          </div>
        </div>

        <!-- 过滤标签：接单、需求互动等业务流程标签 -->
        <div class="filter-tags"></div>
      </div>

      <!-- 订单列表区域 -->
      <div v-if="total > 0" class="order-list">
        <!-- 订单项循环渲染 -->
        <div v-for="(order, index) in orderList" :key="order.contactUserCode" class="order-item">
          <!-- 订单左侧：订单基本信息 -->
          <div class="order-item-left">
            <div class="order-header">
              <a-tooltip>
                <template #title>
                  <div>节点：{{ ProcessNode.ofType(order.processNode)?.desc }}</div>
                  <div>状态：{{ OrderListConstant.ofType(order.state)?.desc }}</div>
                </template>
                <span
                  class="tag"
                  :style="'margin-left: 14px;background:' + ProcessNode.ofType(order.processNode)?.color"
                  >{{ OrderListConstant.ofType(order.state)?.desc }}</span
                >
              </a-tooltip>
              <span v-if="order.state == 1410" style="margin-left: 14px; color: red">{{
                order.state == 1410 ? '竞价流标' : ''
              }}</span>
              <span v-if="getRatio(order)" style="margin-left: 14px">{{ getRatio(order) }}</span>
              <span class="process-time" v-show="judgeAuthority(order, 'time')">
                <img src="@/assets/image/orderList/dealTime.png" width="15" />
                <span>处理时长：{{ dealTimeMap[order.mainCode] }}</span>
                <img
                  v-if="order.isUrgent"
                  src="@/assets/image/orderList/urgent.png"
                  width="20"
                  style="margin-left: 10px"
                />
              </span>
              <span class="capacity" v-show="false">
                <img class="icon02" src="@/assets/image/orderList/personDeal.png" width="15" />
                <img class="icon12" src="@/assets/image/orderList/icon12.png" width="15" />{{
                  order.operatorName
                }}审批中</span
              >
              <span class="capacity1" v-show="showTip(order)">
                <img src="@/assets/image/orderList/warn.png" width="15" />所有方案已经互动完成，将于{{
                  showTip(order)
                }}后自动终止互动。</span
              >
              <span
                class="capacity2"
                v-show="
                  [110, 120, 310, 320, 620, 910].includes(order.state) &&
                  (order.processNode === order.reverseProcessNode ||
                    order.processNode === order.reverseAfterProcessNode)
                "
              >
                <!-- <img src="@/assets/image/orderList/warn.png" width="15" /> -->
                <a-tooltip placement="topLeft">
                  <template #title>{{ order.finalReverseReason }}</template>
                  原因：{{ order.finalReverseReason }}
                </a-tooltip>
              </span>
            </div>
            <div class="order-header">
              <a-tooltip>
                <template #title> {{ order.miceName || '-' }}</template>
                <span class="title" style="margin-left: 14px">
                  {{ order.miceName || '-' }}
                </span>
              </a-tooltip>
              <a-tooltip>
                <template #title>{{ order.pdVerName }}</template>
                <span class="detail">{{ order.pdVerName }}</span>
              </a-tooltip>

              <a-divider type="vertical" />
              <span class="order-no" @click="getCopy(order.mainCode)"
                >{{ order.mainCode }}
                <img class="icon11" src="@/assets/image/orderList/icon11.png" width="15" />
                <img class="icon01" src="@/assets/image/orderList/file.png" width="15" />
              </span>
              <a-divider type="vertical" />
              <span class="enterPerson" v-show="order.personTotal">
                <img src="@/assets/image/orderList/enterPerson.png" width="15" />{{ order.personTotal }}人参会</span
              >
              <a-divider type="vertical" />
              <a-tooltip>
                <template #title>{{ order.cityNames }}</template>
                <span class="location" v-show="order.cityNames">{{ order.cityNames }}</span>
              </a-tooltip>
            </div>
            <div class="order-content">
              <div class="info-row">
                <span
                  ><i class="label">会议日期：</i
                  >{{
                    order.startDate
                      ? order.startDate.split(' ')[0].replaceAll('-', '/') +
                        '-' +
                        order.endDate.split(' ')[0].replaceAll('-', '/')
                      : '-'
                  }}</span
                >
                <span v-show="order.operatorName"
                  ><i class="label">经办人：</i>{{ order.operatorName }}{{ order.operatorName ? '/' : ''
                  }}{{ order.operatorCode }}

                  <a-tooltip placement="top">
                    <template #title>
                      <span v-show="order.operatorPhone">{{ order.operatorPhone }}</span>
                    </template>
                    <img
                      v-show="order.operatorPhone"
                      style="margin-left: 5px; cursor: pointer"
                      @click="getCopy(order.operatorPhone)"
                      src="@/assets/image/orderList/personDeal.png"
                      width="15"
                    />
                  </a-tooltip>
                </span>
                <span
                  ><i class="label">会议顾问：</i
                  >{{ order.consultantUserName ? order.consultantUserName + '/' + order.consultantUserCode : '-' }}
                  <a-tooltip placement="top">
                    <template #title>
                      <span>{{ order.consultantUserPhone }}</span>
                    </template>
                    <img
                      @click="getCopy(order.consultantUserPhone)"
                      v-show="order.consultantUserPhone"
                      style="margin-left: 5px; cursor: pointer"
                      src="@/assets/image/orderList/personDeal.png"
                      width="15"
                    /> </a-tooltip
                ></span>
              </div>
              <div class="info-row">
                <span><i class="label">方案截止：</i>{{ order.schemeDeadline || '-' }}</span>
                <span><i class="label">竞价截止：</i>{{ order.biddingDeadline || '-' }}</span>
              </div>

              <div class="info-row">
                <div class="row">
                  <i class="label">中标服务商：</i>
                  <div class="rowDetail" v-show="order?.winTheBidMerchants?.length === 0">-</div>
                  <div class="rowDetail" v-show="order?.winTheBidMerchants?.length > 0">
                    <a-popover placement="top">
                      <template #content>
                        <p class="p1" v-for="(item, idx) in order.winTheBidMerchants" :key="idx">
                          {{ idx + 1 + '、' + item.merchantName }}
                        </p>
                      </template>
                      <template #title>
                        <span class="title1">{{ '共有' + order.winTheBidMerchants?.length + '家中标服务商' }}</span>
                      </template>
                      <div class="detail5" :style="props.type == 'user' ? 'max-width:10.5vw' : ''">
                        <span>{{ getCompany(order.winTheBidMerchants) }}</span>
                      </div>
                      <div class="detail2">
                        {{ order.winTheBidMerchants?.length }}
                      </div>
                    </a-popover>
                  </div>
                </div>
                <div class="row">
                  <i class="label">服务商对接人：</i>
                  <div class="rowDetail" v-show="order.winTheBidMerchants?.length === 0">-</div>
                  <div class="rowDetail" v-show="order.winTheBidMerchants?.length > 0">
                    <a-popover placement="top">
                      <template #content>
                        <p class="p1" v-for="(item, idx) in order.winTheBidMerchants" :key="idx">
                          {{ idx + 1 + '、' + item.merchantName + ' - ' + item.contactMerchantName }}
                        </p>
                      </template>
                      <template #title>
                        <span class="title1">
                          {{ '共有' + order.winTheBidMerchants?.length + '家中标服务商对接人' }}
                        </span>
                      </template>
                      <div class="detail3">{{ order.winTheBidMerchants?.length }}</div>
                      {{ getContact(order.winTheBidMerchants) }}
                    </a-popover>
                  </div>
                </div>
                <div class="row">
                  <i class="label">中标总金额：</i
                  ><span>{{ order.winTheBidTotalPrices ? order.winTheBidTotalPrices + '元' : '-' }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 订单右侧：操作按钮区域 -->
          <div class="order-item-right" v-show="!route.query.username">
            <Button
              class="primary"
              type="primary"
              @click="handleDynamicBtnClick(order)"
              v-show="judgeAuthority(order) && showBtn(order) != ''"
            >
              <img src="@/assets/image/orderList/btn1.png" width="15" />
              {{ showBtn(order) }}
            </Button>
            <Button
              v-if="!['DEMAND_SUBMIT'].includes(order.processNode)"
              class="plain"
              @click="btnJump(order, '', '2')"
            >
              <img src="@/assets/image/orderList/btn2.png" width="15" />
              查看详情
            </Button>

            <a-dropdown :trigger="['click']">
              <Button class="plain">
                <img src="@/assets/image/orderList/btn3.png" width="15" />
                更多操作
              </Button>
              <template #overlay>
                <a-menu>
                  <a-menu-item
                    v-if="props.type === 'manage' && authority == 'M0'"
                    style="text-align: center"
                    @click="specialPowers(order)"
                  >
                    <a href="javascript:;">特殊权限设置</a>
                  </a-menu-item>
                  <a-menu-item
                    v-if="props.type === 'manage' && ['MICE_EXECUTION'].includes(order.processNode)"
                    style="text-align: center"
                    @click="jumpUser(order)"
                  >
                    <a href="javascript:;">跳转会中</a>
                  </a-menu-item>
                  <a-menu-item
                    v-if="props.type === 'user' && !['DEMAND_SUBMIT'].includes(order.processNode)"
                    style="text-align: center"
                    @click="orderCopy(order)"
                  >
                    <a href="javascript:;">订单复制</a>
                  </a-menu-item>
                  <a-menu-item style="text-align: center" @click="showApprovalModal(order)">
                    <a href="javascript:;">查看审批列表</a>
                  </a-menu-item>
                  <a-menu-item style="text-align: center">
                    <a href="javascript:;">加急</a>
                  </a-menu-item>
                  <a-menu-item
                    v-if="['MICE_PENDING', 'MICE_EXECUTION'].includes(order.processNode) && authority == 'M0'"
                    style="text-align: center"
                    @click="showSwitchBidSchemeModal(order)"
                  >
                    <a href="javascript:;">切换中标方案</a>
                  </a-menu-item>
                  <a-menu-item
                    v-if="
                      ![
                        'DEMAND_SUBMIT',
                        'DEMAND_RECEIVE',
                        'DEMAND_PRE_INTERACT',
                        'DEMAND_CONFIRM',
                        'DEMAND_APPROVAL',
                        'DEMAND_PUSH',
                      ].includes(order.processNode) && props.type === 'manage'
                    "
                    @click="showPush(order)"
                    style="text-align: center"
                  >
                    <a href="javascript:;">查看发布服务商</a>
                  </a-menu-item>
                  <a-menu-item
                    v-if="
                      ['BIDDING', 'BID_RESULT_CONFIRM'].concat(arrConfirmView).includes(order.processNode) &&
                      props.type === 'manage'
                    "
                    style="text-align: center"
                    @click="showBid(order)"
                  >
                    <a href="javascript:;">查看竞价服务商</a>
                  </a-menu-item>
                  <a-menu-item
                    style="text-align: center"
                    v-if="
                      authority !== 'M0' &&
                      ['MICE_PENDING', 'MICE_EXECUTION', 'MICE_COMPLETED'].includes(order.processNode)
                    "
                    @click="schemeBargainingBtn(order, 'bargaining')"
                  >
                    <a href="javascript:;">方案议价</a>
                  </a-menu-item>
                  <a-menu-item
                    style="text-align: center"
                    v-if="authority == 'M1' && order.processNode == 'SCHEME_SUBMIT'"
                    @click="overProcess(order)"
                  >
                    <a href="javascript:;">结束方案提报</a>
                  </a-menu-item>
                  <!--  -->
                  <a-menu-item
                    v-if="props.type === 'user' && order.orderStatus && order.confirmStatus != 1"
                    style="text-align: center"
                    @click="presentConfirm(order)"
                  >
                    <a href="javascript:;">礼品确认</a>
                  </a-menu-item>
                  <a-menu-item
                    v-if="props.type === 'user' && order.shipmentStatus && !order.receiptStatus"
                    style="text-align: center"
                    @click="takeDelivery(order)"
                  >
                    <a href="javascript:;">确认收货</a>
                  </a-menu-item>
                  <a-menu-item
                    style="text-align: center"
                    v-if="authority == 'M1' && order.processNode == 'SCHEME_SUBMIT' && showTip(order)"
                    @click="cancelProcess(order)"
                  >
                    <a href="javascript:;">取消自动结束提报</a>
                  </a-menu-item>
                  <a-menu-item
                    style="text-align: center"
                    v-if="
                      authority == 'M0' &&
                      ['SCHEME_SUBMIT', 'SCHEME_APPROVAL', 'SCHEME_CONFIRM', 'BID_RESULT_CONFIRM'].includes(
                        order.processNode,
                      )
                    "
                    @click="
                      cancelSchemeModal = true;
                      Object.assign(currentOrder, order);
                    "
                  >
                    <a href="javascript:;">撤回重新发布</a>
                  </a-menu-item>
                  <a-modal
                    :ok-button-props="{ loading: loadingModal }"
                    title="撤回重新发布"
                    v-model:open="cancelSchemeModal"
                    @ok="cancelScheme"
                    @cancel="cancelSchemeRemark = ''"
                  >
                    <p>请输入撤回原因！</p>
                    <a-textarea
                      v-model:value="cancelSchemeRemark"
                      :rows="4"
                      :maxLength="200"
                      placeholder="请输入撤回原因"
                    />
                  </a-modal>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
        </div>
        <!-- 分页 -->
        <div class="pagination">
          <Pagination
            v-model:current="searchParams.pageNum"
            v-model:pageSize="searchParams.pageSize"
            :total="total"
            :show-size-changer="true"
            :show-quick-jumper="true"
            :show-total="(total: number) => `共 ${total} 条`"
            @change="handlePageChange"
          />
        </div>
      </div>
      <a-empty v-if="total === 0" />
      <!-- 特殊权限弹窗 -->
      <Modal
        title="特殊权限"
        :open="specialPowersVisible"
        @cancel="specialPowersVisible = false"
        :footer="null"
        width="60%"
        destroyOnClose
      >
        <a-table
          bordered
          :columns="specialPowersColumns"
          :data-source="specialPowersList"
          :pagination="false"
          size="middle"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <a-button @click="powerOpen(record)" v-if="record.openState == 10" type="link">开通</a-button>
              <a-button v-else @click="powerDetail(record)" type="link">查看详情</a-button>
            </template>
            <template v-else-if="column.key === 'openState'">
              {{ record[column.key] == 10 ? '未开通' : '已开通' }}
            </template>
            <template v-else>
              {{ record[column.key] || '-' }}
            </template>
          </template>
        </a-table>
      </Modal>
      <Modal
        title="特殊权限详情"
        :open="powerDetailVisible"
        @cancel="powerDetailVisible = false"
        width="60%"
        destroyOnClose
      >
        <div class="power-detail">权限名称：{{ powerDetailObj.type || '-' }}</div>
        <div class="power-detail">权限影响范围：{{ powerDetailObj.powerScope || '-' }}</div>
        <div class="power-detail">操作人：{{ powerDetailObj.operatorName || '-' }}</div>
        <div class="power-detail">开通人：{{ powerDetailObj.openName || '-' }}</div>
        <div class="power-detail">开通时间：{{ powerDetailObj.onTime || '-' }}</div>
        <template #footer>
          <a-button key="submit" type="primary" @click="powerDetailVisible = false">确定</a-button>
        </template>
      </Modal>
      <!-- 审批列表弹窗 -->
      <Modal
        title="审批列表"
        :open="approvalModalVisible"
        @cancel="closeApprovalModal"
        :footer="null"
        width="60%"
        destroyOnClose
      >
        <a-table :columns="columns" :data-source="approvalList" :pagination="false" size="middle">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <a @click="viewApprovalDetail(record)">查看</a>
              <a
                @click="cancelApproval(record)"
                v-show="currentApprovalOrder.operatorCode == store.loginUser.username && record.state === '10'"
                style="margin-left: 8px"
                >撤销</a
              >
            </template>
            <template v-if="column.key === 'state'">
              {{ ApproveListConstant.ofType(record.state).desc }}
            </template>
          </template>
        </a-table>
      </Modal>

      <!-- 费用支付 - 弹窗 -->
      <a-modal title="费用支付" :open="feesPayShow" @cancel="closeApprovalModal" width="700px" destroyOnClose>
        <div class="demand_pay_fees mt20">
          <div class="demand_pay_fees_label">您的中标方案金额：</div>
          <div class="demand_pay_fees_value">{{ formatNumberThousands(payWinTheBidTotalPrices) + ' 元' }}</div>
        </div>
        <div class="demand_pay_fees mt12" v-if="platformFeeRate">
          <div class="demand_pay_fees_label">用户平台服务费：</div>
          <div class="demand_pay_fees_value">
            {{ formatNumberThousands(platformFee) + ' 元' }}
          </div>
        </div>
        <div class="demand_pay_fees mt12" v-if="platformFeeRate">
          <div class="demand_pay_fees_label">合计：</div>
          <div class="demand_pay_fees_value">
            {{ formatNumberThousands(payWinTheBidTotalPrices + platformFee) + ' 元' }}
          </div>
        </div>
        <template #footer>
          <a-button key="submit" type="primary" @click="PayHandleOk">确定</a-button>
        </template>
      </a-modal>

      <a-modal
        :ok-button-props="{ loading: loadingModal }"
        title="结束提报"
        v-model:open="finishModal"
        @ok="onOk"
        @cancel="finishRemark = ''"
      >
        <p>请输入提前结束原因，结束后供应商将无法提报方案！</p>
        <a-textarea v-model:value="finishRemark" :rows="4" :maxLength="200" placeholder="请输入驳回原因" />
      </a-modal>

      <a-modal title="竞价列表" width="60%" v-model:open="showBidPushed" :footer="null">
        <a-table style="margin-top: 10px" :columns="bidedColumn" :data-source="bidedData" :pagination="false" bordered>
          <template #bodyCell="{ text, record, index, column }">
            <div v-if="column.dataIndex === 'scheme'">
              <div v-for="item in record.scheme">{{ item.bidMerchantName }}</div>
            </div>
            <div v-else>{{ record[column.dataIndex] }}</div>
          </template>
        </a-table>
        <div class="footer">
          <a-button
            type="primary"
            :loading="loadingModal"
            @click="
              () => {
                loadingModal = true;
                router.push({ path: '/bidman/orderList/index', query: { status: '0' } });
                showBidPushed = false;
                isCloseLastTab = true;
                loadingModal = false;
              }
            "
            >确定</a-button
          >
        </div>
      </a-modal>
      <a-modal
        title="变更记录"
        :open="changeSchemeRecord"
        @cancel="changeSchemeRecord = false"
        width="1000px"
        destroyOnClose
      >
        <a-spin :spinning="bargainingLoading">
          <a-table
            :columns="priceChangeColumnsScheme"
            :data-source="priceChangeListScheme"
            :row-key="(record: { id: string; }) => record.id"
            :pagination="false"
            bordered
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <span v-html="record[column.dataIndex]"></span>
            </template>
          </a-table>
        </a-spin>

        <template #footer>
          <!-- <a-button @click="closeBargainingModal">关闭</a-button> -->
          <a-button type="primary" :loading="loadingModal" v-if="isCanPriceChange" @click="changeScheme"
            >切换方案</a-button
          >
        </template>
      </a-modal>
      <!-- 方案议价 - 弹窗 -->
      <a-modal
        :maskClosable="false"
        title="方案议价"
        :open="bargainingShow"
        width="1000px"
        destroyOnClose
        @cancel="closeBargainingModal"
      >
        <a-spin :spinning="bargainingLoading">
          <a-table
            :columns="priceChangeColumns"
            :data-source="priceChangeList"
            :row-key="(record: { id: string; }) => record.id"
            :pagination="false"
            bordered
            size="small"
          >
          </a-table>
        </a-spin>

        <template #footer>
          <!-- <a-button @click="closeBargainingModal">关闭</a-button> -->
          <a-button type="primary" @click="bargainingBtn">发起议价</a-button>
        </template>
      </a-modal>

      <a-modal
        title="发起议价"
        :open="bargainingStartShow"
        @ok="bargainingStartOk"
        @cancel="closeBargainingStartModal"
        :confirmLoading="priceChangeLoading"
        width="700px"
        destroyOnClose
      >
        <div class="bargaining_confirm_modal_list">
          <div class="bargaining_confirm_modal_label">议价资料：</div>
          <div class="bargaining_confirm_modal_value">
            <a-upload
              v-model:fileList="attachmentList"
              :custom-request="uploadRequest"
              :multiple="false"
              :max-count="10"
              :before-upload="beforeUpload"
              @remove="handleRemove"
            >
              <a-button>
                <upload-outlined></upload-outlined>
                上传
              </a-button>
            </a-upload>
            <div class="mt8">文件最大不超过50M</div>
          </div>
        </div>
        <div class="bargaining_confirm_modal_list mt20">
          <div class="bargaining_confirm_modal_label">议价原因：</div>
          <div class="bargaining_confirm_modal_value">
            <a-textarea
              v-model:value="priceChangeRemarks"
              style="width: calc(100% -100px)"
              allow-clear
              :maxlength="500"
            />
          </div>
        </div>
      </a-modal>

      <!-- 议价确认 - 弹窗 -->
      <a-modal
        title="议价确认"
        :open="bargainingConfirmShow"
        @ok="bargainingConfirmOk"
        @cancel="closeBargainingConfirmModal"
        :confirmLoading="priceChangeLoading"
        width="700px"
        destroyOnClose
      >
        <a-spin :spinning="bargainingLoading">
          <div class="bargaining_confirm_modal_list">
            <div class="bargaining_confirm_modal_label">议价资料：</div>
            <div class="bargaining_confirm_modal_value">
              <span v-for="(item, index) in applyAttachmentObj" :key="item.url">
                <span v-show="index > 0" class="mr5">，</span>
                <a @click="fileClick(item.url)">
                  {{ item.name }}
                </a>
              </span>
            </div>
          </div>
          <div class="bargaining_confirm_modal_list mt20">
            <div class="bargaining_confirm_modal_label">议价原因：</div>
            <div class="bargaining_confirm_modal_value">
              {{ firstBargainingRecord.reason }}
            </div>
          </div>
          <div class="bargaining_confirm_modal_list mt20">
            <div class="bargaining_confirm_modal_label">是否通过：</div>
            <div class="bargaining_confirm_modal_value">
              <a-radio-group v-model:value="confirmState" name="radioGroup">
                <a-radio :value="1">通过</a-radio>
                <a-radio :value="2">驳回</a-radio>
              </a-radio-group>
            </div>
          </div>
          <div class="bargaining_confirm_modal_list mt20" v-if="confirmState === 2">
            <span class="bargaining_confirm_modal_label">驳回原因：</span>
            <div class="bargaining_confirm_modal_value">
              <a-textarea v-model:value="confirmReason" allow-clear :maxlength="500" />
            </div>
          </div>
        </a-spin>
      </a-modal>

      <!-- 凭证上传弹框 -->
      <RefundVoucherModal
        v-model:visible="voucherModalVisible"
        :order-data="currentVoucherOrder"
        :type="voucherType"
        @success="handleVoucherSuccess"
      />

      <a-modal
        title="切换中标方案"
        :ok-button-props="{ loading: loadingModal }"
        :open="switchBidSchemeModal"
        @ok="handleSwitchBidSchemeOk"
        @cancel="handleSwitchBidSchemeCancel"
        width="60%"
        destroyOnClose
      >
        <a-spin :spinning="switchBidLoading">
          <div style="margin-bottom: 16px">
            <a-table :data-source="switchBidSchemeTableData" :pagination="false" row-key="id" size="small">
              <a-table-column title="备选方案" dataIndex="name" key="name" v-slot="{ record }">
                <a-tooltip placement="topLeft">
                  <template #title>
                    <div v-for="(item, index) in record.schemeDetails" :key="item.id">{{ item.merchantName }}</div>
                  </template>
                  <div v-for="(item, index) in record.schemeDetails" :key="item.id" v-show="index == 0">
                    {{ item.merchantName }}...
                  </div>
                </a-tooltip>
              </a-table-column>
              <a-table-column
                title="方案金额"
                dataIndex="schemeCombinationTotalPrice"
                key="schemeCombinationTotalPrice"
                v-slot="{ record }"
                >{{ record.schemeCombinationTotalPrice + '元' }}</a-table-column
              >
              <a-table-column :width="100" title="操作" align="center" key="action" v-slot="{ record }">
                <a-button
                  type="link"
                  @click="selectedSchemeId == record.id ? (selectedSchemeId = '') : (selectedSchemeId = record.id)"
                >
                  {{ selectedSchemeId === record.id ? '已选择' : '选择' }}
                </a-button>
              </a-table-column>
            </a-table>
          </div>
          <div style="margin-bottom: 16px; display: flex; justify-content: space-between">
            <p style="white-space: nowrap">切换原因：</p>
            <a-textarea v-model:value="switchBidSchemeReason" :rows="3" placeholder="请输入切换原因" />
          </div>
          <div style="margin-bottom: 16px">
            <span>切换方案说明：</span>
            <a-upload
              :before-upload="beforeUpload"
              v-model:file-list="switchBidSchemeAttachment"
              :custom-request="uploadRequest"
              @remove="handleRemoveScheme"
              accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
              :multiple="true"
              :max-count="10"
              :showUploadList="{ showRemoveIcon: true }"
            >
              <a-button type="primary">上传文件</a-button>
            </a-upload>
          </div>
        </a-spin>
      </a-modal>
      <a-modal
        v-model:open="approvalModalShow"
        title="已提交如下人员审批"
        width="80%"
        v-if="approvalModalShow"
        :keyboard="false"
        :maskClosable="false"
        :closable="false"
      >
        <div>
          <iframe
            width="100%"
            :src="businessProcess + '?code=' + approveCode + '#/detailsPcSt'"
            frameborder="0"
          ></iframe>
        </div>
        <template #footer>
          <a-button
            @click="
              approvalModalShow = false;
              handleSwitchBidSchemeCancel();
            "
            >确定</a-button
          >
        </template>
      </a-modal>
    </div>
  </a-spin>
</template>

<style scoped lang="less">
.container {
  // padding-top: 50px;
  position: relative;
  // 容器样式
  background: #f5f5f5;
  min-height: 100vh;
  overflow: auto !important;
  min-width: 1100px;
}

.top-section {
  margin-left: 20px;
  position: fixed;
  // top: 0;
  z-index: 999;
  // 顶部区域样式，包含状态栏和搜索框
  background: white;
  padding: 14px 24px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06);
}
.ellipsis {
  white-space: nowrap; /* 防止文本换行 */
  overflow: hidden; /* 隐藏溢出的内容 */
  text-overflow: ellipsis; /* 显示省略符号来代表被修剪的文本 */
}
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-bar {
  margin-left: 10px;
  // 状态切换栏样式，显示全部订单、待处理等状态
  display: flex;
  // gap: 8px;
  :first-child {
    border-radius: 4px 0 0 4px;
  }
  :last-child {
    border-radius: 0 4px 4px 0;
  }
  .status-item {
    white-space: nowrap;
    padding: 6px 12px 6px 16px;
    cursor: pointer;
    // border-right: 1px solid #e7e7e7;
    margin-right: 4px;
    display: flex;
    align-items: center;
    border-radius: 4px;
    color: #666;
    font-size: 14px;
    background: #f2f3f5;

    &.active {
      .order-count {
        border: none;
        // margin-right: 4px;
        // width: 20px;
      }
      border-radius: 4px;
      // margin: 0 4px;
      background: #1868db;
      color: white;
    }

    .count {
      margin-left: 4px;
      background: #f0f2f5;
      color: #666;
      padding: 0 6px;
      border-radius: 10px;
      font-size: 14px;
      line-height: 16px;

      .active & {
        background: rgba(255, 255, 255, 0.3);
        color: white;
      }
    }
  }
}

.filter-tags {
  // 过滤标签样式，包含接单、需求互动等标签
  display: flex;
  gap: 8px;
  flex-wrap: wrap;

  .tag {
    padding: 3px 8px;
    // background: #f7f8fa;
    border-radius: 2px;
    color: #333;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 14px;

    &.active {
      // background: #e6f4ff;
      color: #1677ff;
      .count {
        color: #ffffff;
      }
    }

    .count {
      background: #1677ff;
      padding: 0 4px;
      border-radius: 10px;
      color: #ffffff;
      font-size: 14px;
    }
  }
}

.search-input {
  margin-right: 5px;
  // 搜索区域样式，包含下拉框和搜索框
  display: flex;
  gap: 8px;
  align-items: center;

  .search-label {
    white-space: nowrap;
    color: #333;
    font-size: 14px;
  }

  :deep(.ant-input) {
    width: 210px;
  }

  :deep(.ant-input-affix-wrapper) {
    width: 210px;
    height: 24px;
    border: 1px solid #d9d9d9;
    border-radius: 2px;
    padding: 0 8px;
    font-size: 14px;

    &::placeholder {
      color: #999;
    }
  }

  :deep(.ant-btn) {
    height: 24px;
    padding: 0 8px;
    font-size: 14px;
    border-radius: 2px;
  }
}
.capacity {
  border-radius: 14px;
  border: 1px solid #e5e6e8;
  padding: 2px 8px;
  color: #101010;
  font-size: 14px;
  .icon12 {
    display: none;
  }
}
.capacity:hover {
  cursor: pointer;
  border: 1px solid #1868db;
  color: #1868db;
  .icon12 {
    display: inline-block;
  }
  .icon02 {
    display: none;
  }
}
.rowDetail {
  position: relative;
  display: inline-block;
}
.enterPerson {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #1d2129;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  margin-right: 0px !important;
}
img {
  vertical-align: -10%;
  margin-right: 3px;
}

.capacity2 {
  vertical-align: -40%;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 500px;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid rgba(255, 85, 51, 1);
  padding: 2px 8px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: rgba(255, 85, 51, 1);
  text-align: left;
  font-style: normal;
}
.capacity1 {
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100px;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid rgba(24, 104, 219, 0.2);
  padding: 2px 8px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #1868db;
  text-align: left;
  font-style: normal;
}
.title1 {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #1d2129;
  text-align: left;
  font-style: normal;
}
.p1 {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #4e5969;
  text-align: left;
  font-style: normal;
}
.row {
  background: #f7f8fc;
  white-space: nowrap;
  width: 30%;
  position: relative;
  display: inlnie-block;
}
.order-list {
  margin-top: 60px;
  // 订单列表区域
  padding: 14px;

  .order-item {
    // 单个订单项样式
    display: flex;
    justify-content: space-between;
    background: #ffffff;
    border-radius: 8px;
    padding: 20px 14px;
    height: 100%;
    margin-bottom: 24px;

    .order-item-left {
      // 订单左侧信息区域
      display: inline-block;
      width: 84%;
      align-items: center;
      gap: 14px;
      .order-header {
        // 订单头部，包含标题、编号等信息
        width: 100%;
        span {
          margin-right: 24px;
          white-space: nowrap;
        }
        white-space: nowrap;
        // display: flex;
        // justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        .tag {
          vertical-align: -40%;
          text-overflow: ellipsis;
          overflow: hidden;
          width: 108px;
          display: inline-block;
          padding: 4px 12px;
          // background: #1868db;
          border-radius: 4px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 14px;
          color: #ffffff;
          line-height: 20px;
          text-align: center;
          font-style: normal;
        }

        .title {
          vertical-align: -20%;
          overflow: hidden;
          display: inline-block;
          white-space: nowrap;
          text-overflow: ellipsis;
          max-width: 300px;
          font-weight: 500;
          font-family: PingFangSC, PingFang SC;
          font-weight: 700;
          font-size: 18px;
          color: #1d2129;
          line-height: 25px;
          text-align: left;
          font-style: normal;
        }

        .order-no {
          margin-right: 0px;
          font-family: PingFangSC, PingFang SC;
          position: relative;
          font-weight: 400;
          font-size: 14px;
          color: #86909c;
          line-height: 20px;
          text-align: left;
          font-style: normal;
          cursor: pointer;
          .icon01 {
            display: inline-block;
          }
          .icon11 {
            display: none;
          }
        }
        .order-no:hover .icon11 {
          display: inline-block;
        }
        .order-no:hover .icon01 {
          display: none;
        }
        .location {
          vertical-align: -25%;
          display: inline-block;
          max-width: 120px;
          padding: 0 5px;
          background: #f2f3f5;
          border-radius: 2px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #1d2129;
          line-height: 20px;
          text-align: left;
          font-style: normal;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .detail {
          background: #e3edfb;
          border-radius: 2px;
          padding: 2px 4px;
          cursor: pointer;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 12px;
          color: #1868db;
          line-height: 20px;
          text-align: left;
          font-style: normal;
          margin-right: 0px;
        }
      }
      .label {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #86909c;
        line-height: 20px;
        text-align: right;
        font-style: normal;
      }
      .detail5 {
        vertical-align: -27%;
        display: inline-block;
        max-width: calc(14vw);
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .detail2 {
        display: inline-block;
        font-size: 14px;
        color: #1d2129;
        text-align: center;
        line-height: 16px;
        width: 16px;
        margin-left: 5px;
        height: 16px;
        // position: absolute;
        // bottom: 0px;
        // right: -15px;
        background: #c9cdd4;
        border-radius: 3px;
      }
      .detail3 {
        font-size: 14px;
        color: #1d2129;
        text-align: center;
        line-height: 16px;
        width: 16px;
        height: 16px;
        position: absolute;
        bottom: 0px;
        right: -20px;
        background: #c9cdd4;
        border-radius: 3px;
      }
      .order-content {
        background: #f7f8fc;
        border-radius: 2px;
        padding: 14px;
        // 订单内容，包含会议日期、经办人等详细信息
        .info-row {
          display: flex;
          gap: 32px;
          // justify-content: space-between;
          margin-bottom: 8px;
          color: #666;
          font-size: 14px;

          &:last-child {
            margin-bottom: 0;
          }

          span {
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #1d2129;
            line-height: 20px;
            text-align: left;
            font-style: normal;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
            width: 30%;
            &:nth-child(2),
            &:nth-child(3) {
              .number {
                color: #1677ff;
              }
            }
          }
        }
      }
    }

    .process-time {
      line-height: 15px;
      width: 105px;
      white-space: nowrap;
      color: #101010;
      font-size: 14px;
    }
    .order-item-right {
      // 订单右侧操作区域，包含按钮和处理时长
      position: relative;
      display: flex;
      width: 15%;
      align-items: center;
      justify-content: space-around;
      gap: 8px;
      flex-direction: column;
      padding: 0 8px;
      padding-top: 15px;
      img {
        margin-right: 5px;
      }
      :deep(.ant-btn) {
        width: 168px;
        height: 39.82px;
        font-size: 14px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .primary {
        background: #1868db;
        border-radius: 4px;
      }
      .plain {
        border-radius: 4px;
        border: 1px solid #1868db;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        // font-size: 14px;
        color: #1868db;
        line-height: 20px;
        text-align: center;
        font-style: normal;
      }
    }
  }
}

.primary1 {
  background: #1868db;
  border-radius: 4px;
  border: none;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  line-height: 22px;
  text-align: left;
  font-style: normal;
}
.plain1 {
  background: #e8f3ff;
  border: none;
  border-radius: 4px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #1868db;
  line-height: 22px;
  text-align: right;
  font-style: normal;
}
// .detail4 {
//   display: inline-block;
//   width: 200px; /* 需要设置一个宽度 */
//   overflow: hidden;
//   white-space: nowrap;
//   text-overflow: ellipsis;
// }
.pagination {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
  padding: 0 24px;
}
.order-count {
  margin-left: 4px;
  // margin-right: -10px;
  font-weight: bold;
  display: inline-block;
  border-radius: 0 !important;
}
:deep(.ant-divider) {
  margin: 0 16px;
  color: #e5e6eb;
  height: 20px;
}
:deep(.ant-empty) {
  margin-top: 200px;
}

.demand_pay_fees {
  font-family: PingFangSC, PingFang SC;
  font-size: 16px;
  line-height: 22px;
  display: flex;

  .demand_pay_fees_label {
    width: 160px;
    text-align: right;
    color: #1d2129;
  }
  .demand_pay_fees_value {
    text-indent: 8px;
    font-weight: 500;
    color: #1868db;
  }
}

.footer {
  margin-top: 20px;
  text-align: center;
}
:deep(.ant-dropdown-menu-item) {
  padding: 9px 12px !important;
}

.bargaining_confirm_modal_list {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;

  .bargaining_confirm_modal_label {
    width: 80px;
    display: inline-block;
  }
  .bargaining_confirm_modal_value {
    display: inline-block;
    width: calc(100% - 80px);
  }
}
.power-detail {
  font-size: 16px;
  margin-top: 10px;
  font-weight: bold;
}
</style>
