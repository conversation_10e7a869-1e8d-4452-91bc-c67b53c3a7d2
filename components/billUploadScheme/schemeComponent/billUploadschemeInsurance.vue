<script setup lang="ts">
// 方案互动-保险产品
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, nextTick, defineProps, defineEmits } from 'vue';
import { UploadOutlined } from '@ant-design/icons-vue';

import { errorModal, resolveParam, routerParam, formatNumberThousands } from '@haierbusiness-front/utils';
import { fileApi } from '@haierbusiness-front/apis';

const props = defineProps({
  schemeItem: {
    type: Object,
    default: {},
  },
  schemeCacheItem: {
    type: Object,
    default: {},
  },
  isSchemeCache: {
    type: Boolean,
    default: false,
  },
  schemeIndex: {
    type: Number,
    default: 0,
  },
  schemeType: {
    // 方案提报类型 // 查看需求-view / 未提报-notReported / 已提报-reported / 查看方案-schemeView / 待竞价 - notBidding / 竞价完成 - biddingView / 账单上传 - billUpload
    type: String,
    default: '',
  },
  readonly: {
    // 是否为只读模式
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['schemePriceEmit', 'schemeInsurancesEmit']);

const oldSchemeList = ref<any[]>([]);
const newSchemeList = ref<any[]>([]);

const subtotal = ref<number>(0); // 小计

// 计算左侧小计（需求数据）
const getLeftSubtotal = () => {
  let leftSubtotal = 0;
  console.log(oldSchemeList.value,"oldSchemeList.value");
  
  oldSchemeList.value.forEach((e) => {
    if (e.demandUnitPrice && (e.personNum || e.schemePersonNum)) {
      leftSubtotal += e.demandUnitPrice * (e.personNum || e.schemePersonNum);
    }
  });
  return leftSubtotal;
};

// 价格计算
const priceCalcFun = () => {
  subtotal.value = 0;

  newSchemeList.value.forEach((e) => {
    // 根据模式选择计算方式
    if (props.schemeType === 'billUpload') {
      if (e.billUnitPrice && e.billPersonNum) {
        subtotal.value += e.billUnitPrice * e.billPersonNum;
      }
    } else {
      if (e.schemeUnitPrice && e.schemePersonNum) {
        subtotal.value += e.billUnitPrice * e.billPersonNum;
      }
    }
  });
  console.log("保险小计",subtotal.value);
  
  emit('schemePriceEmit', { type: 'insurance', totalPrice: subtotal.value, schemeIndex: props.schemeIndex });
};

// 人数变化时重新计算价格
const changePersonNum = (index: number) => {
  console.log(oldSchemeList.value,"oldSchemeList.value");
  
  priceCalcFun();

  // 触发暂存
  insuranceTempSave();
};

// 账单人数变化时重新计算价格
const changeBillPersonNum = (index: number) => {
  // 账单模式下使用billPersonNum和billUnitPrice计算
  calculateBillPrice();

  // 触发暂存
  insuranceTempSave();
};

// 账单单价变化时重新计算价格
const changeBillUnitPrice = (index: number) => {
  // 账单模式下使用billPersonNum和billUnitPrice计算
  calculateBillPrice();

  // 触发暂存
  insuranceTempSave();
};

// 计算账单价格
const calculateBillPrice = () => {
  subtotal.value = 0;
  newSchemeList.value.forEach((e) => {
    if (props.schemeType === 'billUpload') {
      if (e.billUnitPrice && e.billPersonNum) {
        subtotal.value += e.billUnitPrice * e.billPersonNum;
      }
    } else {
      if (e.schemeUnitPrice && e.schemePersonNum) {
        subtotal.value += e.schemeUnitPrice * e.schemePersonNum;
      }
    }
  });

  emit('schemePriceEmit', { type: 'insurance', totalPrice: subtotal.value, schemeIndex: props.schemeIndex });
};

watch([() => props.schemeItem, () => props.schemeCacheItem], ([newScheme, newCache], [oldScheme, oldCache]) => {
    oldSchemeList.value = JSON.parse(JSON.stringify(newScheme))?.insurances || [];
    // console.log(oldSchemeList.value,"oldSchemeList.value");
    
    if (
      props.isSchemeCache &&
      props.schemeCacheItem &&
      props.schemeCacheItem.insurances &&
      props.schemeCacheItem.insurances.length > 0 &&
      !props.readonly // 🔥 查看模式下不使用缓存
    ) {
      // 编辑模式：缓存 - 反显（只有当缓存数据存在且不为空时才使用）
      
      
      newSchemeList.value = props.schemeCacheItem.insurances.map((cacheItem: any, idx: number) => {
        // 从原始数据中获取对应的项目，确保数据完整性
        const originalItem = oldSchemeList.value[idx];

        return {
          // 使用缓存数据
          ...cacheItem,

          // 确保关键字段正确映射（从原始数据补充）
          miceDemandInsuranceId: cacheItem.miceDemandInsuranceId || originalItem?.id,
          miceSchemeInsuranceId: cacheItem.miceSchemeInsuranceId || cacheItem.id,

          // 确保必要字段存在
          insuranceName: cacheItem.insuranceName || originalItem?.insuranceName,
          insuranceContent: cacheItem.insuranceContent || originalItem?.insuranceContent,
          productId: cacheItem.productId || originalItem?.productId,
          productMerchantId: cacheItem.productMerchantId || originalItem?.productMerchantId,

          // 确保人数和单价字段
          schemePersonNum: cacheItem.schemePersonNum || originalItem?.personNum,
          schemeUnitPrice: cacheItem.schemeUnitPrice || originalItem?.demandUnitPrice,

          // 账单相关字段（账单上传模式）
          billPersonNum: cacheItem.billPersonNum || cacheItem.schemePersonNum || originalItem?.personNum,
          billUnitPrice: cacheItem.billUnitPrice !== undefined ? cacheItem.billUnitPrice : (cacheItem.schemeUnitPrice || originalItem?.demandUnitPrice),

          // 其他字段
          sourceId: cacheItem.sourceId || originalItem?.sourceId || null,
          invoiceTempId: cacheItem.invoiceTempId || null,
          statementTempId: cacheItem.statementTempId || null,

          // 保险附件字段（缓存回显）
          insuranceAttachments: cacheItem.insuranceAttachments || [],
          attachmentTempId: cacheItem.attachmentTempId || null,
        };
      });

      // 价格计算
      nextTick(() => {
        priceCalcFun();
      });
    } else {
      console.log('第二步！！！');
      // 没有缓存或缓存为空时，使用原始数据初始化
      const demandData = JSON.parse(JSON.stringify(newScheme))?.insurances || [];
      newSchemeList.value = demandData.map((e: any) => {

        // 直接复制所有字段，确保右边显示和左边一致
        const rightSideData = {
          // 保持原有字段
          ...e,

          // 确保必要的ID字段
          miceDemandInsuranceId: e.id,
          miceSchemeInsuranceId: e.miceSchemeInsuranceId || e.id,

          // 确保人数字段存在（用于编辑）
          schemePersonNum: e.schemePersonNum || e.personNum,

          // 确保单价字段存在
          schemeUnitPrice: e.schemeUnitPrice || e.demandUnitPrice,

          // 确保其他必要字段
          sourceId: e.sourceId || null,

          // 保险附件字段初始化
          insuranceAttachments: e.insuranceAttachments || [],
          attachmentTempId: e.attachmentTempId || null,
        };

        return rightSideData;

        // 如果是账单上传模式，添加账单相关字段
        if (props.schemeType === 'billUpload') {
          return {
            ...rightSideData,
            // 账单字段初始化
            // 🔥 查看模式使用详情数据，编辑模式使用默认值
            billPersonNum: props.readonly ? e.billPersonNum || e.schemePersonNum || e.personNum : e.schemePersonNum || e.personNum,
            // 🔥 查看模式使用详情数据，编辑模式使用默认值
            billUnitPrice: props.readonly
              ? (e.billUnitPrice !== undefined ? e.billUnitPrice : (e.schemeUnitPrice || e.demandUnitPrice || null))
              : null,
            // 🔥 查看模式使用详情数据，编辑模式使用默认值
            invoiceTempId: props.readonly ? e.invoiceTempId || null : null,
            statementTempId: props.readonly ? e.statementTempId || null : null,

            // 确保保险附件字段也被包含
            // 🔥 查看模式使用详情数据，编辑模式使用默认值
            insuranceAttachments: props.readonly ? e.insuranceAttachments || [] : [],
            attachmentTempId: props.readonly ? e.attachmentTempId || null : null,
          };
        }

        return rightSideData;
      });

    }

    // 小计 - 根据模式选择计算方式
    subtotal.value = 0;
    newSchemeList.value.forEach((e) => {
      if (props.schemeType === 'billUpload') {
        if (e.billUnitPrice && e.schemePersonNum) {
          subtotal.value += e.billUnitPrice * e.schemePersonNum;
        }
      } else {
        if (e.schemeUnitPrice && e.schemePersonNum) {
          subtotal.value += e.schemeUnitPrice * e.schemePersonNum;
        }
      }
    });

    emit('schemePriceEmit', { type: 'insurance', totalPrice: subtotal.value, schemeIndex: props.schemeIndex });
  },
  {
    immediate: true,
    deep: true,
  },
);

const schemePlanLabelList = ['参保人数', '保险产品', '单价'];

const changePrice = (index: number) => {
  if (newSchemeList.value[index].biddingPrice) {
    newSchemeList.value[index].planPrice =
      newSchemeList.value[index].biddingPrice * newSchemeList.value[index].schemePersonNum;
  }

  // 价格计算
  priceCalcFun();
};

// 方案数据转换为账单数据
const convertSchemeToAccountData = (schemeData: any) => {
  return {
    // 直接对应字段
    sourceId: schemeData.sourceId || null,
    miceDemandInsuranceId: schemeData.miceDemandInsuranceId,
    miceSchemeInsuranceId: schemeData.miceSchemeInsuranceId || schemeData.miceDemandInsuranceId,
    demandDate: schemeData.demandDate,
    demandUnitPrice: schemeData.demandUnitPrice,
    productId: schemeData.productId,
    productMerchantId: schemeData.productMerchantId,
    insuranceName: schemeData.insuranceName,
    insuranceContent: schemeData.insuranceContent,
    description: schemeData.description,

    // 转换字段：方案 → 账单
    billPersonNum: schemeData.billPersonNum || schemeData.schemePersonNum || 0,
    billUnitPrice: schemeData.billUnitPrice !== undefined ? schemeData.billUnitPrice : (schemeData.schemeUnitPrice || null),

    // 新增账单字段（临时ID，需要从发票和水单上传功能获取）
    invoiceTempId: schemeData.invoiceTempId || null,
    statementTempId: schemeData.statementTempId || null,

    // 保单附件相关字段
    insuranceAttachments: schemeData.insuranceAttachments || [],
    attachmentTempId: schemeData.attachmentTempId || null,

    // 保留原有方案字段（用于显示对比）
    schemePersonNum: schemeData.schemePersonNum,
    schemeUnitPrice: schemeData.schemeUnitPrice,
  };
};

// 更新保险项目的发票临时ID
const updateInsuranceInvoiceId = (insuranceIndex: number, invoiceTempId: string | null) => {
  if (newSchemeList.value[insuranceIndex]) {
    newSchemeList.value[insuranceIndex].invoiceTempId = invoiceTempId;
    insuranceTempSave();
  }
};

// 更新保险项目的水单临时ID
const updateInsuranceStatementId = (insuranceIndex: number, statementTempId: string | null) => {
  if (newSchemeList.value[insuranceIndex]) {
    newSchemeList.value[insuranceIndex].statementTempId = statementTempId;
    insuranceTempSave();
  }
};

// 更新保险项目的附件临时ID和路径 - 修改为给所有保险数据都添加附件
const updateInsuranceAttachmentId = (insuranceIndex: number, attachmentData: any) => {
  if (attachmentData && newSchemeList.value.length > 0) {
    // 为所有保险项目都添加相同的附件数据
    newSchemeList.value.forEach((item, index) => {
      item.attachmentTempId = attachmentData.tempId;
      // 使用拼接好的完整URL数组
      item.insuranceAttachments = attachmentData.insuranceAttachments || [];
    });

    insuranceTempSave();
  }
};

// 获取提交数据（过滤掉UI相关字段）
const getSubmitData = () => {
  const submitData = newSchemeList.value.map(item => {
    if (props.schemeType === 'billUpload') {
      return convertSchemeToAccountData(item);
    }
    return item;
  });

  return submitData;
};

// 暂存
const insuranceTempSave = () => {
  // 根据schemeType决定数据格式
  const processedData = props.schemeType === 'billUpload'
    ? newSchemeList.value.map(item => convertSchemeToAccountData(item))
    : [...newSchemeList.value];
  console.log(processedData,"processedData");
  
  emit('schemeInsurancesEmit', {
    schemeInsurances: processedData,
    schemeIndex: props.schemeIndex,
  });
};
// 校验
const insuranceSub = () => {
  let isVerPassed = true;

  // newSchemeList.value.forEach((e, i) => {
  //   if (!e.schemeUnitPrice) {
  //     message.error('请输入' + e.demandDate + '保险' + (i + 1) + '竞价单价');

  //     isVerPassed = false;
  //     return;
  //   }
  // });

  if (isVerPassed) {
    insuranceTempSave();
  }

  return isVerPassed;
};

// 文件上传相关方法
const beforeUpload = async (file: any, insuranceIndex: number) => {
  // 文件类型验证
  const isValidType = file.type === 'application/pdf' ||
                     file.type.startsWith('image/') ||
                     file.name.toLowerCase().endsWith('.doc') ||
                     file.name.toLowerCase().endsWith('.docx');

  if (!isValidType) {
    message.error('只支持上传 PDF、图片、Word 文档！');
    return false;
  }

  // 文件大小验证（10MB）
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error('文件大小不能超过 10MB！');
    return false;
  }

  try {
    // 上传文件
    const formData = new FormData();
    formData.append('file', file);

    const uploadRes = await fileApi.uploadFile(formData);

    if (uploadRes && uploadRes.path) {
      // 更新对应保险项目的附件
      if (!newSchemeList.value[insuranceIndex].insuranceAttachments) {
        newSchemeList.value[insuranceIndex].insuranceAttachments = [];
      }

      // 添加完整的URL路径
      const fullUrl = uploadRes.path.startsWith('http') ? uploadRes.path :
                     `${import.meta.env.VITE_BUSINESS_URL}${uploadRes.path}`;

      newSchemeList.value[insuranceIndex].insuranceAttachments.push(fullUrl);

      // 更新UI显示的文件列表
      if (!newSchemeList.value[insuranceIndex].attachmentFiles) {
        newSchemeList.value[insuranceIndex].attachmentFiles = [];
      }

      newSchemeList.value[insuranceIndex].attachmentFiles.push({
        uid: Date.now().toString(),
        name: file.name,
        status: 'done',
        url: fullUrl,
        filePath: uploadRes.path
      });

      message.success('文件上传成功！');

      // 触发暂存
      insuranceTempSave();
    }
  } catch (error) {
    message.error('文件上传失败！');
  }

  return false; // 阻止默认上传行为
};

const removeFile = (file: any, insuranceIndex: number) => {
  // 从附件列表中移除
  if (newSchemeList.value[insuranceIndex].insuranceAttachments) {
    const index = newSchemeList.value[insuranceIndex].insuranceAttachments.findIndex(
      (url: string) => url === file.url
    );
    if (index > -1) {
      newSchemeList.value[insuranceIndex].insuranceAttachments.splice(index, 1);
    }
  }

  // 从UI文件列表中移除
  if (newSchemeList.value[insuranceIndex].attachmentFiles) {
    const fileIndex = newSchemeList.value[insuranceIndex].attachmentFiles.findIndex(
      (f: any) => f.uid === file.uid
    );
    if (fileIndex > -1) {
      newSchemeList.value[insuranceIndex].attachmentFiles.splice(fileIndex, 1);
    }
  }

  // 触发暂存
  insuranceTempSave();
};

const previewFile = (file: any) => {
  if (file.url) {
    window.open(file.url, '_blank');
  }
};

const removeAttachment = (insuranceIndex: number, attachmentIndex: number) => {
  if (newSchemeList.value[insuranceIndex].insuranceAttachments) {
    newSchemeList.value[insuranceIndex].insuranceAttachments.splice(attachmentIndex, 1);

    // 同时从UI文件列表中移除对应项
    if (newSchemeList.value[insuranceIndex].attachmentFiles) {
      newSchemeList.value[insuranceIndex].attachmentFiles.splice(attachmentIndex, 1);
    }

    // 触发暂存
    insuranceTempSave();
  }
};

const viewAttachment = (attachmentUrl: string) => {
  window.open(attachmentUrl, '_blank');
};

const getFileName = (url: string) => {
  if (!url) return '';
  const parts = url.split('/');
  return parts[parts.length - 1] || '附件';
};

defineExpose({
  insuranceSub,
  insuranceTempSave,
  updateInsuranceInvoiceId,
  updateInsuranceStatementId,
  updateInsuranceAttachmentId,
  getSubmitData
});

onMounted(async () => {
  console.log(oldSchemeList.value,"oldSchemeList");
  
});
</script>

<template>
  <!-- 保险产品 -->
  <div class="scheme_vehicle">
    <div class="common_table">
      <!-- 左侧 -->
      <div class="common_table_l" v-if="props.schemeType !== 'notBidding' && props.schemeType !== 'biddingView'">
        <div class="scheme_plan_title">
          <div class="scheme_plan_img mr10"></div>
          <span>保险需求</span>
        </div>

        <div class="scheme_plan_table mt20" v-for="(item, idx) in oldSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '保险产品' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value pl12 pr12">
              {{ (item.personNum || item.schemePersonNum) ? (item.personNum || item.schemePersonNum) + '人' : '-' }}
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.insuranceName || '-' }}
                </template>
                {{ item.insuranceName || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.demandUnitPrice ? item.demandUnitPrice + '元/人' : '-' }}
                </template>
                {{ item.demandUnitPrice ? item.demandUnitPrice + '元/人' : '-' }}
              </a-tooltip>
            </div>
          </div>

          <!-- 保单附件显示区域 -->
          <div v-if="item.insuranceAttachments && item.insuranceAttachments.length > 0" class="scheme_plan_attachments mt12">
            <div class="attachment_title">保单附件：</div>
            <div class="attachment_list">
              <a-tag
                v-for="(attachment, attachIdx) in item.insuranceAttachments"
                :key="attachIdx"
                @click="viewAttachment(attachment)"
                style="cursor: pointer; margin-right: 8px; margin-bottom: 4px;"
                color="blue"
              >
                <file-text-outlined style="margin-right: 4px;" />
                {{ getFileName(attachment) }}
              </a-tag>
            </div>
          </div>

          <!-- 左侧总额显示区域 -->
          <div class="scheme_plan_list3 pr12">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">总额：</div>
              <div class="scheme_plan_price_value">
                <!-- 账单上传模式使用方案单价和账单人数计算 -->
                <template v-if="props.schemeType === 'billUpload'">
                  {{
                    item.billUnitPrice && item.billPersonNum
                      ? '¥' + formatNumberThousands(item.billUnitPrice * item.billPersonNum)
                      : (item.demandUnitPrice && (item.personNum || item.schemePersonNum)
                          ? '¥' + formatNumberThousands(item.demandUnitPrice * (item.personNum || item.schemePersonNum))
                          : '-')
                  }}
                </template>
                <!-- 其他模式使用需求数据计算 -->
                <template v-else>
                  {{
                    item.demandUnitPrice && (item.personNum || item.schemePersonNum)
                      ? '¥' + formatNumberThousands(item.demandUnitPrice * (item.personNum || item.schemePersonNum))
                      : '-'
                  }}
                </template>
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <!-- 账单上传模式显示需求计算公式（左侧始终显示需求数据） -->
              <div v-if="props.schemeType === 'billUpload' && item.demandUnitPrice && (item.personNum || item.schemePersonNum)">
                {{ (item.personNum || item.schemePersonNum) + '人*' + item.demandUnitPrice + '(需求单价)' }}
              </div>
              <!-- 其他模式显示需求计算公式 -->
              <div v-else-if="props.schemeType !== 'billUpload' && item.demandUnitPrice && (item.personNum || item.schemePersonNum)">
                {{ (item.personNum || item.schemePersonNum) + '人*' + item.demandUnitPrice + '(需求单价)' }}
              </div>
            </div>
          </div>
        </div>

        <!-- 左侧小计显示 -->
        <div v-show="getLeftSubtotal()" class="scheme_plan_subtotal mt16">
          {{ '小计：¥' + formatNumberThousands(getLeftSubtotal()) }}
        </div>
      </div>

      <div class="common_table_divide"></div>

      <!-- 右侧 -->
      <div class="common_table_r">
        <div class="scheme_plan_title">
          <div class="scheme_plan_img mr10"></div>
          <span>{{ props.schemeType === 'billUpload' ? '账单信息' : '保险方案' }}</span>
        </div>

        <div class="scheme_plan_table mt20" v-for="(item, idx) in newSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '保险产品' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value p0">
              <!-- 🔥 查看模式 - 只显示文本 -->
              <div
                class="pl12"
                v-if="
                  props.readonly ||
                  props.schemeType === 'notBidding' ||
                  props.schemeType === 'biddingView' ||
                  props.schemeType === 'schemeView'
                "
              >
                <a-tooltip placement="topLeft">
                  <template #title>
                    <!-- 账单上传模式显示账单人数，其他模式显示方案人数 -->
                    {{
                      props.schemeType === 'billUpload'
                        ? (item.billPersonNum || item.schemePersonNum ? (item.billPersonNum || item.schemePersonNum) + '人' : '-')
                        : (item.schemePersonNum ? item.schemePersonNum + '人' : '-')
                    }}
                  </template>
                  {{
                    props.schemeType === 'billUpload'
                      ? (item.billPersonNum || item.schemePersonNum ? (item.billPersonNum || item.schemePersonNum) + '人' : '-')
                      : (item.schemePersonNum ? item.schemePersonNum + '人' : '-')
                  }}
                </a-tooltip>
              </div>
              <!-- 账单上传模式 - 编辑账单人数 -->
              <div class="pl12" v-else-if="props.schemeType === 'billUpload'">
                <a-tooltip placement="topLeft">
                  <template #title v-if="item.billPersonNum">
                    {{ item.billPersonNum ? item.billPersonNum + '人' : '-' }}
                  </template>
                  <a-input-number
                    v-model:value="item.billPersonNum"
                    @change="changeBillPersonNum(idx)"
                    style="width: calc(100% - 30px)"
                    placeholder="账单人数"
                    :min="1"
                    :max="999999"
                    :precision="0"
                    :bordered="false"
                    :controls="false"
                    allow-clear
                  />
                  <div class="scheme_plan_edit"></div>
                </a-tooltip>
              </div>
              <!-- 其他编辑模式 - 编辑方案人数 -->
              <div class="pl12" v-else>
                <a-tooltip placement="topLeft">
                  <template #title v-if="item.schemePersonNum">
                    {{ item.schemePersonNum ? item.schemePersonNum + '人' : '-' }}
                  </template>
                  <a-input-number
                    v-model:value="item.schemePersonNum"
                    @change="changePersonNum(idx)"
                    style="width: calc(100% - 30px)"
                    placeholder="人数"
                    :min="1"
                    :max="999999"
                    :precision="0"
                    :bordered="false"
                    :controls="false"
                    allow-clear
                  />
                  <div class="scheme_plan_edit"></div>
                </a-tooltip>
              </div>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.insuranceName || '-' }}
                </template>
                {{ item.insuranceName || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <!-- 账单上传模式显示账单单价（只读） -->
              <div v-if="props.schemeType === 'billUpload'">
                <a-tooltip placement="topLeft">
                  <template #title>
                    {{ (item.billUnitPrice !== null && item.billUnitPrice !== undefined) ? item.billUnitPrice + '元/人' : (item.schemeUnitPrice ? item.schemeUnitPrice + '元/人' : '-') }}
                  </template>
                  {{ (item.billUnitPrice !== null && item.billUnitPrice !== undefined) ? item.billUnitPrice + '元/人' : (item.schemeUnitPrice ? item.schemeUnitPrice + '元/人' : '-') }}
                </a-tooltip>
              </div>
              <!-- 其他模式显示方案单价 -->
              <div v-else>
                <a-tooltip placement="topLeft">
                  <template #title>
                    {{ item.schemeUnitPrice ? item.schemeUnitPrice + '元/人' : '-' }}
                  </template>
                  {{ item.schemeUnitPrice ? item.schemeUnitPrice + '元/人' : '-' }}
                </a-tooltip>
              </div>
            </div>
          </div>
          <div class="scheme_plan_list3 pr12">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">总额：</div>
              <div class="scheme_plan_price_value">
                <!-- 账单上传模式使用方案单价和账单人数计算 -->
                <template v-if="props.schemeType === 'billUpload'">
                  {{
                    item.billUnitPrice && item.billPersonNum
                      ? '¥' + formatNumberThousands(item.billUnitPrice * item.billPersonNum)
                      : (item.schemeUnitPrice && item.schemePersonNum
                          ? '¥' + formatNumberThousands(item.schemeUnitPrice * item.schemePersonNum)
                          : '-')
                  }}
                </template>
                <!-- 其他模式使用方案数据计算 -->
                <template v-else>
                  {{
                    item.schemeUnitPrice && item.schemePersonNum
                      ? '¥' + formatNumberThousands(item.schemeUnitPrice * item.schemePersonNum)
                      : '-'
                  }}
                </template>
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <!-- 账单上传模式显示账单计算公式 -->
              <div v-if="props.schemeType === 'billUpload' && item.billUnitPrice && item.billPersonNum">
                {{ item.billPersonNum + '人*' + item.billUnitPrice + '(账单单价)' }}
              </div>
              <!-- 其他模式显示方案计算公式 -->
              <div v-else-if="props.schemeType !== 'billUpload' && item.schemeUnitPrice && item.schemePersonNum">
                {{ item.schemePersonNum + '人*' + item.schemeUnitPrice + '(单价)' }}
              </div>
            </div>
          </div>


        </div>

        <div v-show="subtotal" class="scheme_plan_subtotal mt16">
          {{ '小计：¥' + formatNumberThousands(subtotal) }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.scheme_vehicle {
  .scheme_plan_img {
    background: url('@/assets/image/demand/demand_insurance.png');
    background-repeat: no-repeat;
    background-size: 18px 18px;
    background-position: left center;
  }

  // 人数输入框样式，模仿活动组件
  .scheme_plan_value {
    :deep(.ant-input-number) {
      border: none;
      box-shadow: none;

      .ant-input-number-input {
        height: auto;
        padding: 0;
        text-align: left;
        width: 100%;
        font-weight: normal;
        font-size: 14px;
        color: #333;
        border-bottom: none;
        border: none;

        &::placeholder {
          color: #bfbfbf;
        }
      }

      &:hover .ant-input-number-input,
      &:focus .ant-input-number-input,
      &.ant-input-number-focused .ant-input-number-input {
        border: none;
        box-shadow: none;
      }
    }

    .scheme_plan_edit {
      margin-left: 5px;
      display: inline-flex;
      vertical-align: middle;

      width: 16px;
      height: 18px;
      background: url('@/assets/image/common/edit_gray.png');
      background-repeat: no-repeat;
      background-size: 16px 16px;
    }
  }

  :deep(.ant-input-number .ant-input-number-input) {
    height: 24px;
    padding: 0 5px;
    text-align: end;

    width: 84px;
    font-weight: 500;
    font-size: 14px;
    color: #1868db;
    text-align: right;
    border-bottom: 1px solid #4e5969;
  }

  .p0 {
    padding: 0 !important;
  }
}
</style>
