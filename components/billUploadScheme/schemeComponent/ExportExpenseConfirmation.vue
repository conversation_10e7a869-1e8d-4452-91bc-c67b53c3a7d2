<script setup lang="ts">
import { message } from 'ant-design-vue';
import { ref } from 'vue';
import { schemeApi, fileApi } from '@haierbusiness-front/apis';
import { QuestionCircleOutlined } from '@ant-design/icons-vue';

// Props 接收父组件传递的数据验证函数
const props = defineProps({
  // 验证所有数据是否完整的函数
  validateAllData: {
    type: Function,
    required: true,
  },
  // 获取导出数据的函数
  getExportData: {
    type: Function,
    required: true,
  },
  // 当前方案类型
  schemeType: {
    type: String,
    default: 'billUpload',
  },
});

const exportLoading = ref(false);
const importLoading = ref(false);
const hasImportedSettlement = ref(false); // 是否已导入结算单
const importedFileName = ref(''); // 导入的文件名
const hasExported = ref(false); // 是否已导出过结算单
const importedFilePath = ref(''); // 导入的文件路径

// 获取基础URL
const baseUrl = import.meta.env.VITE_BUSINESS_URL || '';

// 数据完整性验证
const validateDataIntegrity = async (): Promise<boolean> => {
  try {
    // 调用父组件的验证函数，复用完成提报的校验逻辑
    const isValid = await props.validateAllData();

    if (!isValid) {
      return false;
    }

    return true;
  } catch (error) {
    return false;
  }
};

// 导出功能
const handleExport = async () => {
  exportLoading.value = true;

  try {

    // 第一步：数据完整性验证
    const isDataValid = await validateDataIntegrity();
    if (!isDataValid) {
      return;
    }

    // 第二步：获取导出数据
    const exportData = await props.getExportData();
    if (!exportData) {
      message.error('获取导出数据失败！');
      return;
    }


    // 第三步：调用导出接口
    const response = await schemeApi.exportExpenseConfirmation(exportData);
    
    // 处理文件下载
    if (response && response.data) {
      // 如果返回的是文件流，则触发下载
      const blob = new Blob([response.data], { 
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
      });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `费用确认明细_${new Date().getTime()}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    }

    message.success('费用确认导出成功！');
    hasExported.value = true; // 标记已导出
  } catch (error) {
  } finally {
    exportLoading.value = false;
  }
};

// 导入功能
const handleImport = async () => {
  // 检查是否已导出
  if (!hasExported.value) {
    message.warning('请先导出结算单后再进行导入操作！');
    return;
  }

  // 创建文件输入元素
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = '.xlsx,.xls';
  
  input.onchange = async (e: any) => {
    const file = e.target.files[0];
    if (!file) return;
    
    importLoading.value = true;
    
    try {
      
      // 验证文件格式
      const fileName = file.name;
      const fileExt = fileName.substring(fileName.lastIndexOf('.')).toLowerCase();
      if (!['.xlsx', '.xls'].includes(fileExt)) {
        message.error('请选择Excel文件！');
        return;
      }
      
      // 实际的文件上传逻辑
      const formData = new FormData();
      formData.append('file', file);
      
      try {
        // 调用文件上传API
        const response = await fileApi.upload(formData);
        const filePath = response.path ? baseUrl + response.path : ''; // 拼接完整路径
        
        // 导入成功
        hasImportedSettlement.value = true;
        importedFileName.value = fileName;
        importedFilePath.value = filePath;
        
        message.success('结算单导入成功！');
      } catch (uploadError) {
        console.error('文件上传失败:', uploadError);
        message.error('文件上传失败，请重试！');
        hasImportedSettlement.value = false;
        importedFileName.value = '';
        importedFilePath.value = '';
      }
    } catch (error) {
      message.error('导入失败，请重试！');
      hasImportedSettlement.value = false;
      importedFileName.value = '';
      importedFilePath.value = '';
    } finally {
      importLoading.value = false;
    }
  };
  
  input.click();
};

// 暴露给父组件的方法，检查是否已导入结算单
defineExpose({
  hasImportedSettlement,
  importedFileName,
  importedFilePath,
  hasExported
});
</script>

<template>
  <div class="export-expense-confirmation">
    <div class="contract_title">
      <div class="interact_shu mr20"></div>
      <span>结算单上传</span>
      <a-tooltip class="ml10">
        <template #title>
          导出结算单后，线下盖章，将盖完章的结算单重新导入
        </template>
        <QuestionCircleOutlined />
      </a-tooltip>
    </div>
    <div class="contract_wrapper">
      <div class="export-item">
        <div class="button-group">
          <a-button :loading="exportLoading" @click="handleExport">导出结算单</a-button>
          <a-button 
            :loading="importLoading" 
            :disabled="!hasExported"
            @click="handleImport">
            导入结算单
          </a-button>
          <span v-if="hasImportedSettlement" class="import-status">
            <span class="success-icon">✓</span>
            已导入: {{ importedFileName }}
          </span>
          <span v-else-if="!hasExported" class="import-hint">
            请先导出结算单
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
*{
  font-family: PingFangSC, PingFang SC;
}
.export-expense-confirmation {
  padding-top: 24px;

  .interact_shu {
    width: 4px;
    height: 20px;
    background: #1868db;
    border-radius: 2px;
  }

  .contract_title {
    margin-bottom: 20px;
    font-size: 18px;
    color: #333;
    // font-weight: 500;
    display: flex;
    align-items: center;
    // gap: 8px;

    span {
      font-size: 18px;
      // font-weight: 500;
      color: #1d2129;
    }
  }

  .contract_wrapper {
    border-radius: 6px;
    overflow: hidden;
    width: 70%;

    .export-item {
      display: flex;
      align-items: center;
      padding: 18px 30px;
      padding-top: 0px;
      background-color: #fff;
      transition: background-color 0.3s;

      // &:hover {
      //   background-color: #fafafa;
      // }

      .export-label {
        width: 140px;
        font-size: 14px;
        color: #333;
        margin-right: 40px;
        flex-shrink: 0;
        font-weight: 500;
      }

      .button-group {
        flex: 1;
        display: flex;
        align-items: center;
        gap: 24px;

        :deep(.ant-btn) {
          height: 36px;
          font-size: 14px;
          padding: 0 20px;
          border-radius: 4px;

          &[type='primary'] {
            background-color: #1890ff;
            border-color: #1890ff;

            &:hover {
              background-color: #40a9ff;
              border-color: #40a9ff;
            }
          }

          &:not([type='primary']) {
            border-color: #d9d9d9;
            color: #666;

            &:hover {
              border-color: #1890ff;
              color: #1890ff;
            }
          }
        }

        .import-status {
          margin-left: 16px;
          font-size: 14px;
          color: #52c41a;
          display: flex;
          align-items: center;
          
          .success-icon {
            margin-right: 6px;
            font-size: 16px;
            font-weight: bold;
          }
        }

        .import-hint {
          margin-left: 16px;
          font-size: 14px;
          color: #999;
          font-style: italic;
        }
      }
    }
  }
}

.mr20 {
  margin-right: 20px;
}

.ml10 {
  margin-left: 10px;
}
</style>
