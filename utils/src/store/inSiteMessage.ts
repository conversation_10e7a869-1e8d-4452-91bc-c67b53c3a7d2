import { IInSiteMessage, Result } from '@haierbusiness-front/common-libs';
import { inSiteMessageApi } from '@haierbusiness-front/apis';
import { notification, Button, message } from 'ant-design-vue';
import { h } from 'vue';
import { defineStore } from 'pinia'

interface INotificationLoadingType {
    key: string
    loading: boolean
}

type InSiteMessageState = {
    // 站内信列表
    messageList: Array<IInSiteMessage> | null
    loading: boolean
    messageTotal: number
    notificationLoadings: Array<INotificationLoadingType>
    pollingTimer: NodeJS.Timeout | null
    isPolling: boolean
}

// 站内信
export const inSiteMessageStore = defineStore('inSiteMessage', {
    state: (): InSiteMessageState => {
        return {
            messageList: null,
            messageTotal: 0,
            loading: false,
            notificationLoadings: [],
            // 新增轮询相关状态
            pollingTimer: null as NodeJS.Timeout | null,
            isPolling: false
        }
    },
    actions: {
        // 获取站内信列表并对比本地缓存，如果有新增的，则覆盖本地缓存并弹出通知
        async getMessageList() {
            this.loading = true;
            // 获取站内信列表
            const details = await inSiteMessageApi.oneSelfList({ pageNum: 1, pageSize: 5, isRead: 1 });
            this.messageTotal = details.total || 0

            if (this.messageList == null) {
                // 初始值为null，不需要提醒。
                this.messageList = details.records || [];
            }
            else {
                // 不是首次查询时，右侧需要弹出提醒
                // 对比本地缓存，如果有新增的，则覆盖本地缓存并弹出通知
                const localMessageList: Array<IInSiteMessage> = this.messageList || [];
                this.messageList = details.records || [];
                const diffMessageList: Array<IInSiteMessage> = this.messageList.filter(item => !localMessageList.some(localItem => localItem.id === item.id));
                // todo: hdx 2025-08-08 暂时没数据，先用messageList代替
                if (diffMessageList.length > 0) {
                    diffMessageList.forEach(item => {
                        this.notificationLoadings.push({
                            key: item.id.toString(),
                            loading: false
                        })
                        notification.info({
                            message: `😃您有一条新的${item.subject}，请及时查看`,
                            description: item.content,
                            placement: 'bottomRight',
                            duration: 0,
                            key: item.id.toString(),
                            btn: () =>
                                h(
                                  Button,
                                  {
                                    type: 'primary',
                                    size: 'small',
                                    loading: this.notificationLoadings.find(o => o.key === item.id.toString())?.loading || false,
                                    onClick: () => {
                                        this.notificationLoadings.find(o => o.key === item.id.toString())!.loading = true
                                        this.readMessage(item.id).then((isSuccess) => {
                                            if (isSuccess) {
                                                notification.close(item.id.toString())
                                            } else {
                                                message.error('操作失败，请重试!')
                                            }
                                        }).finally(() => {
                                            this.notificationLoadings.find(o => o.key === item.id.toString())!.loading = false
                                        })
                                    },
                                  },
                                  { default: () => '我已知晓' },
                                ),
                        });
                    });
                }
            }
            this.loading = false;
        },
        // 消息已读
        async readMessage(id: number): Promise<boolean> {
            const res = await inSiteMessageApi.readMessage(id)
            return res.success
        },

        // 开始轮询
        startPolling(interval: number = 60000) { // 默认60秒
            if (this.isPolling) return;
            
            this.isPolling = true;
            this.pollingTimer = setInterval(() => {
                this.getMessageList();
            }, interval);
        },

        // 停止轮询
        stopPolling() {
            if (this.pollingTimer) {
                clearInterval(this.pollingTimer);
                this.pollingTimer = null;
            }
            this.isPolling = false;
        },
    }
})