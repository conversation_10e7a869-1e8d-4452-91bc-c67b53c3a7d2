<script lang="ts" setup>
import { ref } from 'vue';
import schemeDetails from '../../scheme/schemeDetails.vue';
import schemeChange from '../../scheme/schemeChange.vue';
import billUploadScheme from '@haierbusiness-front/components/billUploadScheme/billUploadschemeDetails.vue';
import { getDealTime, routerParam, resolveParam } from '@haierbusiness-front/utils';
import { useRoute } from 'vue-router';
const route = useRoute();
const record = ref(resolveParam(route?.query?.record) || JSON.parse(route?.query?.record));
const viewSelect = ref('demand');
</script>
<template>
  <div class="container">
    <schemeDetails v-if="viewSelect === 'demand'"> </schemeDetails>
    <schemeChange v-else-if="viewSelect === 'scheme'"> </schemeChange>
    <billUploadScheme v-else-if="viewSelect === 'billUpload'" :platformType="'merchant'"></billUploadScheme>
    <div class="footer">
      <a-radio-group v-model:value="viewSelect" button-style="solid">
        <a-radio-button value="demand">需求视图</a-radio-button>
        <a-radio-button value="scheme">方案视图</a-radio-button>
        <a-radio-button
          value="billUpload"
          v-if="
            [
              'PAYMENT_CONFIRM',
              'PLATFORM_INVOICE_ENTRY',
              'VENDOR_INVOICE_ENTRY',
              'INVOICE_CONFIRM',
              'PLATFORM_REFUND_RECEIPT_UPLOAD',
              'REFUND_CONFIRM',
              'PLATFORM_PAY_RECEIPT_UPLOAD',
              'PLATFORM_INVOICE_CONFIRM',
              'SETTLEMENT_PENDING',
              'SETTLEMENT_RECORDED',
              'END',
            ].includes(record.processNode)
          "
          >账单视图</a-radio-button
        >
      </a-radio-group>
      <a-button type="primary">返回</a-button>
    </div>
  </div>
</template>
<style lang="less" scoped>
.container {
  padding-bottom: 40px;
  width: 100%;
  height: 100%;
  position: relative;
}
.footer {
  right: 0;
  background: #fff;
  z-index: 11;
  width: calc(100% - 220px);
  padding: 10px 20px;
  position: fixed;
  bottom: 0;
  display: flex;
  justify-content: space-between;
}
</style>
