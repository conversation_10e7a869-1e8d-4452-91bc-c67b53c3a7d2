<script setup lang="ts">
import { announcementNoticeApi } from '@haierbusiness-front/apis';
import {
  AnnouncementContentForm,
  IAnnouncementNotice
} from '@haierbusiness-front/common-libs';
import { computed, ref, onMounted, nextTick, reactive, h, watch } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import EditDialog from './edit-dialog.vue'
import router from '../../../../router';
import { ColumnType } from 'ant-design-vue/es/table';
// const router = useRouter()

const currentRouter = ref()

onMounted(async () => {
  currentRouter.value = await router

  // 初始加载数据
  listApiRun({
    effectScope: 2,
    pageNum: 1,
    pageSize: 10
  });
})
const confirmLoading = ref(false);
const {
  data,
  run: listApiRun,
  loading,
} = usePagination(announcementNoticeApi.state);
//可见
const visible = ref(false)
//详情数据
const editData = ref<IAnnouncementNotice>()
//通知详情
const noticeDetails = async (id: number, effectScope: []) => {
  if (!id) return;

  confirmLoading.value = true;
  try {
    const res = await announcementNoticeApi.details(id);
    editData.value = res;
    if (editData.value) {
      console.log(editData.value);
      if (res.contentForm == 2) {
        window.open(res.informContent, '_blank');
      } else {
        visible.value = true
      }

    }
    const params = {
      id,
      effectScope: 2,
      effectScopes: effectScope,
    }
    await announcementNoticeApi.read(params)
      .then(() => {
        listApiRun({
          effectScope: 2,
          pageNum: 1,
          pageSize: 10
        });
      })
  } catch (error) {
    console.error('获取详情失败:', error);
  } finally {
    confirmLoading.value = false;
  }
}

const onDialogClose = () => {
  visible.value = false
}
const handleOk = () => {
  visible.value = false
}

const columns: ColumnType[] = [
  {
    title: '通知标题',
    dataIndex: 'title',
    width: '180px',
    align: 'left',
    ellipsis: true,
  },
  {
    title: '发布时间',
    dataIndex: 'gmtCreate',
    width: '100px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '60px',
    align: 'center'
  },
];

const dataSource = computed(() => data.value?.records || []);

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pageNum: number,
) => {
  // 确保使用最新的搜索参数和过滤参数，优先使用filterInputs中的非空值
  const params = {
    effectScope: 2,
    pageNum: pageNum,
    pageSize: pagination.value.pageSize,
  };
  listApiRun(params);
};

</script>

<template>
  <div style="width: 100%;padding:12px;">
    <a-table class="table" :columns="columns" :row-key="record => record.id" :data-source="dataSource"
      :pagination="false" :loading="loading">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'title'">
          <span :class="record.isRead ? 'read' : 'unread'" @click="noticeDetails(record.id, record.effectScope)">{{
            record.title }}</span>
        </template>
        <template v-if="column.dataIndex === '_operator'">
          <a-button type="link" @click="noticeDetails(record.id, record.effectScope)">查看</a-button>
        </template>
      </template>
    </a-table>
    <div class="flex-center" v-if="dataSource.length > 0">
      <a-pagination class="pagination" v-model:current="pagination.current" :total="pagination.total" show-less-items
        @change="handleTableChange" />
    </div>
  </div>
  <div v-if="visible">
    <edit-dialog :show="visible" :data="editData" @cancel="onDialogClose" @ok="handleOk">
    </edit-dialog>
  </div>
</template>

<style scoped lang="less">
.read {
  color: #86909C;
}

.unread {
  color: #1868DB;
}

.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

.notice-title {
  width: 60%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: black;
  font-size: 14px;
  cursor: pointer;

  &:hover {
    color: #1677ff;
  }
}

:deep(.ant-spin-nested-loading) {
  height: 100%;

  .ant-spin-container {
    height: calc(100% - 80px);
  }
}

.table {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);

  :deep(.ant-table) {
    border-radius: 8px;

    .ant-table-thead {
      tr {
        th {
          padding: 13px 12px;
        }

        .ant-table-cell {
          background: #fff;
          color: #86909C;
          font-weight: 400;

          &:first-child {
            border-top-left-radius: 8px;
          }

          &:last-child {
            border-top-right-radius: 8px;
          }

          &::before {
            opacity: 0;
          }
        }
      }
    }

    .ant-table-tbody {
      tr {
        td {
          padding: 13px 12px;
        }
      }
    }
  }
}

.pagination {

  :deep(.ant-pagination-prev),
  :deep(.ant-pagination-next),
  :deep(.ant-pagination-item) {
    border: 1px solid rgba(0, 0, 0, 0.15);

    .ant-pagination-item-link,
    a {
      color: rgba(0, 0, 0, 0.65);
    }
  }

  :deep(.ant-pagination-item-active) {
    background: #1868DB;

    a {
      color: #fff;
    }
  }

  :deep(.ant-pagination-disabled) {
    .ant-pagination-item-link {
      color: rgba(0, 0, 0, 0.25);
    }
  }
}

.flex-center {
  margin-top: 20px;
}
</style>
