<script setup lang="ts">
// 住宿
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, defineProps, defineEmits, defineExpose, inject, nextTick } from 'vue';
import {
  hotelLevelAllConstant,
  RoomTypeConstant,
  BreakfastTypeConstant,
  StaysArr,
  DemandSubmitObj,
  DemandCalcStayObj,
} from '@haierbusiness-front/common-libs';
import { demandApi } from '@haierbusiness-front/apis';

const props = defineProps({
  dateIndex: {
    type: Number,
    default: 0,
  },
  meetingPersonTotal: {
    // 会议人数
    type: Number,
    default: 0,
  },
  stayList: {
    type: Array,
    default: [],
  },
  hotelList: {
    type: Array,
    default: [],
  },
});

const emit = defineEmits(['demandPlanStayFunc', 'demandPlanRemoveFunc']);

// AI测算价格新增数量
const demandAiCalcAddFunc = inject('demandAiCalcAddFunc', () => {});
const demandAiCalcDelFunc = inject('demandAiCalcDelFunc', () => {});

const demandPlanFormRef = ref();

// 日程安排表单
const formState = reactive<DemandSubmitObj>({
  stays: [], // 住宿
});

const hotelList = ref<Array>([]); // 酒店列表
const hotelChangeObj = ref<DemandCalcStayObj>({}); // 酒店选择

// 酒店选择列表
watch(
  () => props.hotelList,
  (newList) => {
    hotelList.value = newList.filter((e) => e.centerMarker);

    setTimeout(() => {
      // 住宿
      formState.stays.forEach((e, idx) => {
        nextTick(() => {
          calcPrice(idx);
        });

        // 酒店需求只有一个时，默认选中
        if (hotelList.value?.length === 1) {
          e.tempDemandHotelId = hotelList.value[0].tempDemandHotelId;
          e.level = hotelList.value[0].level;

          hotelChangeObj.value = hotelList.value[0];
        }

        if (e.tempDemandHotelId) {
          const checkedHotel = hotelList.value.filter((j) => j.tempDemandHotelId === e.tempDemandHotelId);

          if (checkedHotel.length === 0) {
            e.tempDemandHotelId = null;
            e.level = null;
            hotelChangeObj.value = {};
          }
        }
      });
    }, 0);
  },
  {
    immediate: true,
    deep: true,
  },
);

// // 住宿列表
// watch(
//   () => props.stayList,
//   (newVal) => {
//     formState.stays = [...newVal];
//   },
//   {
//     immediate: true,
//     deep: true,
//   },
// );

// 删除
const removeStay = (type: String, index: Number) => {
  emit('demandPlanRemoveFunc', { type: type, delIndex: index, index: props.dateIndex });
};

// 提交
const onSubmit = async () => {
  let isVerifyPassed = false;

  await demandPlanFormRef.value
    .validate()
    .then(() => {
      emit('demandPlanStayFunc', { list: [...formState.stays], index: props.dateIndex });

      isVerifyPassed = true;
    })
    .catch((err) => {
      isVerifyPassed = false;
    });

  return isVerifyPassed;
};

const changePlanList = () => {
  // 住宿列表
  formState.stays = [...props.stayList];
};

defineExpose({ onSubmit, changePlanList });

// 酒店选择
const changeHotel = (obj: StaysArr, i: Number) => {
  // 星级-赋值
  const hotelObj = hotelList.value.filter((e) => e.tempDemandHotelId == obj.tempDemandHotelId);
  formState.stays[i].level = hotelObj[0]?.level || null;

  hotelChangeObj.value = hotelObj[0] || {};

  calcPrice(i);
};

// 房间数
const changeRoomNum = (i: Number) => {
  // 大床房（默认1人） - 1
  // 双床房 - 2
  // 套房（默认1人） - 3

  if (formState.stays[i].roomType && formState.stays[i].personNum) {
    // 房间容纳人数
    const roomPersonNum = formState.stays[i].roomType === 2 ? 2 : 1;

    const roomNums = formState.stays[i].personNum / roomPersonNum;
    formState.stays[i].roomNum = roomNums ? Math.ceil(roomNums) : null;

    if (
      Number(formState.stays[i].personNum) ===
      Number(formState.stays[i].roomNum) *
        (formState.stays[i].roomType === 1 || formState.stays[i].roomType === 3 ? 1 : 2)
    ) {
      // 人数、房间数匹配时，清空不一致原因
      formState.stays[i].discrepancyReason = '';
    }
    // } else {
    //   formState.stays[i].roomNum = null;
  }

  calcPrice(i);
};

// 价格测算
const calcPrice = async (i: Number) => {
  if (
    formState.stays[i].tempDemandHotelId &&
    formState.stays[i].roomType &&
    formState.stays[i].personNum &&
    formState.stays[i].roomNum &&
    formState.stays[i].level
  ) {
    if (hotelChangeObj.value && Object.keys(hotelChangeObj.value).length === 0) {
      hotelChangeObj.value = hotelList.value[0];
    }

    const calcParams = {
      calcDate: formState.stays[i].demandDate + ' 00:00:00', // 需求日期
      roomType: formState.stays[i].roomType, // 房型
      breakfastType: formState.stays[i].breakfastType, // 早餐类型
      roomNum: formState.stays[i].roomNum, //

      level: formState.stays[i].level, // 酒店星级
      cityId: hotelChangeObj.value?.cityId || null, // 酒店所在城市id
      cityName: hotelChangeObj.value?.cityName || null, //
      districtIds: hotelChangeObj.value?.districtIds || '', // 酒店所在区域id,支持多区域,逗号分割\n 如果多个区域,则多个区域同时测算,并返回最高的区域的价格
      latitude: hotelChangeObj.value?.latitude || '', // 需求中心点经度
      longitude: hotelChangeObj.value?.longitude || '', // 需求中心点纬度
      distanceRange: hotelChangeObj.value?.distanceRange || null, // 需求范围:单位米(可选)
      centerMarker: hotelChangeObj.value?.centerMarker || '', //	需求中心的地标名称
    };

    demandAiCalcAddFunc();

    const res = await demandApi.priceCalcStay({
      ...calcParams,
    });

    formState.stays[i].calcUnitPrice = res.calcUnitPrice;
    demandAiCalcDelFunc();

    console.log(
      '%c [ 住宿-自动测算单价 ]-120',
      'font-size:13px; background:pink; color:#bf2c9f;',
      formState.stays[i],
      res,
    );
  }
};

onMounted(async () => {
  // 住宿列表
  changePlanList();
});
</script>

<template>
  <!-- 住宿 -->
  <div class="stay_com">
    <a-form ref="demandPlanFormRef" :model="formState" :labelCol="{ style: { width: '84px' } }" hideRequiredMark>
      <div class="plan_col_list mb20" v-for="(staysItem, staysIndex) in formState.stays" :key="staysIndex">
        <div class="plan_col_title">
          {{ '住宿' + (staysIndex + 1) }}
        </div>
        <a-popconfirm
          :title="'确认删除住宿' + (staysIndex + 1) + '？'"
          ok-text="确认"
          cancel-text="取消"
          @confirm="removeStay('stay', staysIndex)"
        >
          <div class="plan_col_del"></div>
        </a-popconfirm>

        <a-row :gutter="12" class="mt20">
          <a-col :span="8">
            <a-row>
              <a-col :span="staysItem.level ? 17 : 24">
                <a-form-item
                  label="酒店选择："
                  :name="['stays', staysIndex, 'tempDemandHotelId']"
                  :rules="{
                    required: true,
                    message: '请选择酒店',
                    trigger: 'change',
                  }"
                  tooltip="酒店选择来自酒店需求"
                >
                  <a-select
                    v-model:value="staysItem.tempDemandHotelId"
                    @change="changeHotel(staysItem, staysIndex)"
                    :disabled="hotelList && hotelList.length === 1"
                    placeholder="请选择酒店"
                    allow-clear
                  >
                    <a-select-option
                      v-for="(item, idx) in hotelList"
                      :key="item.tempDemandHotelId"
                      :value="item.tempDemandHotelId"
                    >
                      <a-tooltip placement="topLeft" :title="'酒店' + (idx + 1) + '-' + item.centerMarker">
                        {{ '酒店' + (idx + 1) + '-' + item.centerMarker }}
                      </a-tooltip>
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col v-show="staysItem.level" :span="7">
                <a-form-item label="">
                  <a-select v-model:value="staysItem.level" placeholder="请选择酒店星级" disabled>
                    <a-select-option
                      v-for="item in hotelLevelAllConstant.toArray()"
                      :key="item.code"
                      :value="item.code"
                    >
                      {{ item.desc }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </a-col>

          <a-col :span="8">
            <a-row :gutter="4">
              <a-col :span="19">
                <a-form-item
                  label="酒店房型："
                  :name="['stays', staysIndex, 'roomType']"
                  :rules="{
                    required: true,
                    message: '请选择房型',
                    trigger: 'change',
                  }"
                >
                  <a-select
                    v-model:value="staysItem.roomType"
                    @change="changeRoomNum(staysIndex)"
                    placeholder="请选择房型"
                    allow-clear
                  >
                    <a-select-option v-for="item in RoomTypeConstant.toArray()" :key="item.code" :value="item.code">
                      {{ item.desc }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="5">
                <a-form-item
                  label=""
                  :name="['stays', staysIndex, 'breakfastType']"
                  :rules="{
                    required: true,
                    message: '请选择早餐',
                    trigger: 'change',
                  }"
                >
                  <a-select v-model:value="staysItem.breakfastType" placeholder="请选择早餐" allow-clear>
                    <a-select-option
                      v-for="item in BreakfastTypeConstant.toArray()"
                      :key="item.code"
                      :value="item.code"
                    >
                      {{ item.desc }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </a-col>

          <a-col :span="8">
            <a-form-item
              label="住宿人数："
              :name="['stays', staysIndex, 'personNum']"
              :rules="{
                required: true,
                message: '请填写住宿人数',
                trigger: 'change',
              }"
            >
              <a-input-number
                v-model:value="staysItem.personNum"
                @blur="changeRoomNum(staysIndex)"
                placeholder="请填写住宿人数"
                allow-clear
                :min="1"
                :max="props.meetingPersonTotal || 99999"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item
              label="房间数："
              :name="['stays', staysIndex, 'roomNum']"
              :rules="{
                required: true,
                message: '请填写房间数',
                trigger: 'change',
              }"
            >
              <a-input-number
                v-model:value="staysItem.roomNum"
                @blur="calcPrice(staysIndex)"
                placeholder="请填写房间数"
                allow-clear
                :min="1"
                :max="999999"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>

          <a-col
            :span="8"
            v-if="
              staysItem.roomNum &&
              staysItem.roomType &&
              staysItem.personNum &&
              Number(staysItem.personNum) !==
                Number(staysItem.roomNum) * (staysItem.roomType === 1 || staysItem.roomType === 3 ? 1 : 2)
            "
          >
            <!--
              房型分为大床（1人），双床（两人），套房（1人），
              当人数和（房间数*房型1,2）不等，需要显示不一致原因，必填。
            -->
            <a-form-item
              label="不一致原因："
              :name="['stays', staysIndex, 'discrepancyReason']"
              :rules="{
                required: true,
                message: '请填写不一致原因',
                trigger: 'change',
              }"
            >
              <a-input
                v-model:value="staysItem.discrepancyReason"
                placeholder="请填写不一致原因"
                :maxlength="500"
                allow-clear
              />
            </a-form-item>
          </a-col>
        </a-row>
      </div>
    </a-form>
  </div>
</template>

<style scoped lang="less">
.stay_com {
  .plan_col_list {
    padding: 20px 24px;
    background: #fff;
    border-radius: 8px;

    position: relative;

    .plan_col_title {
      background: url('@/assets/image/demand/demand_stay.png');
      background-repeat: no-repeat;
      background-size: 18px 18px;
      background-position: left center;

      text-indent: 28px;
      font-weight: 500;
      font-size: 18px;
      line-height: 25px;
      color: #1d2129;
    }

    .plan_col_del {
      position: absolute;
      top: 12px;
      right: 12px;

      width: 18px;
      height: 18px;
      background: url('@/assets/image/demand/demand_del_x.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;

      cursor: pointer;
    }
  }
}
</style>
