export const demandJsonAdd = {
  // mainCode: '',
  demandRejectReason: '',
  //
  miceName: '智能填报会议（请修改名称）',
  miceType: 5,
  personTotal: 100,
  contactUserName: '王忠明',
  contactUserCode: '24060822',
  contactUserPhone: '15615738641',
  contactUserEmail: '<EMAIL>',
  districtType: 0,
  //
  remarks: '智能填报测试需求',
  hotels: [
    {
      id: 600,
      sourceId: null,
      provinceId: 23,
      provinceName: '山东',
      cityId: 59,
      cityName: '青岛',
      districtIds: '428,1370,1991,2236,2257,2667,2738,2851,2915,2966',
      districtNames: '崂山区,胶州,城阳区,李沧区,市北区,平度,莱西,即墨区,市南区,黄岛区',
      wishId: null,
      wishAreaId: null,
      wishCode: null,
      level: 48,
      latitude: '36.062687',
      longitude: '120.384599',
      distanceRange: null,
      centerMarker: '五四广场',
    },
  ],
  startDate: '2025-10-01',
  endDate: '2025-10-02',
  stays: [
    {
      id: 2246,
      sourceId: null,
      miceDemandHotelId: 600,
      demandDate: '2025-10-01',
      roomType: 2,
      breakfastType: 1,
      personNum: 90,
      roomNum: 45,
      discrepancyReason: '',
      calcUnitPrice: 507.0,
    },
    {
      id: 2247,
      sourceId: null,
      miceDemandHotelId: 600,
      demandDate: '2025-10-01',
      roomType: 1,
      breakfastType: 1,
      personNum: 10,
      roomNum: 10,
      discrepancyReason: '',
      calcUnitPrice: 331.0,
    },
    {
      id: 2248,
      sourceId: null,
      miceDemandHotelId: 600,
      demandDate: '2025-10-02',
      roomType: 2,
      breakfastType: 1,
      personNum: 95,
      roomNum: 48,
      discrepancyReason: '存在需单人住宿的情况',
      calcUnitPrice: 38.0,
    },
    {
      id: 2249,
      sourceId: null,
      miceDemandHotelId: 600,
      demandDate: '2025-10-02',
      roomType: 3,
      breakfastType: 0,
      personNum: 5,
      roomNum: 5,
      discrepancyReason: '',
      calcUnitPrice: 579.0,
    },
  ],
  places: [
    {
      id: 1141,
      sourceId: null,
      miceDemandHotelId: 600,
      demandDate: '2025-10-01',
      usageTime: 6,
      usagePurpose: 2,
      personNum: 100,
      area: null,
      underLightFloor: 4,
      tableType: 5,
      hasLed: true,
      ledNum: 10,
      ledSpecs: '以服务商提报为准',
      hasTea: false,
      teaEachTotalPrice: null,
      teaDesc: null,
      calcUnitPlacePrice: 53836.0,
      calcUnitLedPrice: 472.0,
      calcUnitTeaPrice: null,
    },
    {
      id: 1142,
      sourceId: null,
      miceDemandHotelId: 600,
      demandDate: '2025-10-02',
      usageTime: 3,
      usagePurpose: 5,
      personNum: 100,
      area: null,
      underLightFloor: null,
      tableType: 5,
      hasLed: true,
      ledNum: 10,
      ledSpecs: 'LED',
      hasTea: true,
      teaEachTotalPrice: 50.0,
      teaDesc: '茶歇',
      calcUnitPlacePrice: 19868.0,
      calcUnitLedPrice: 2790.0,
      calcUnitTeaPrice: 50.0,
    },
  ],
  caterings: [
    {
      miceDemandHotelId: 600,
      id: 1142,
      sourceId: null,
      isInsideHotel: true,
      demandDate: '2025-10-01',
      cateringType: 1,
      cateringTime: 1,
      personNum: 100,
      demandUnitPrice: 100.0,
      isIncludeDrinks: true,
      calcUnitPrice: 100.0,
    },
    {
      miceDemandHotelId: 600,
      id: 1143,
      sourceId: null,
      isInsideHotel: false,
      demandDate: '2025-10-02',
      cateringType: 3,
      cateringTime: 0,
      personNum: 100,
      demandUnitPrice: 80.0,
      isIncludeDrinks: false,
      calcUnitPrice: 80.0,
    },
  ],
  vehicles: [
    {
      id: 1137,
      sourceId: null,
      demandDate: '2025-10-01',
      usageType: 1,
      usageTime: 1,
      seats: 19,
      vehicleNum: 5,
      brand: '丰田',
      route: '从青岛胶东国际机场到八大关风景区，再从八大关风景区到五四广场',
      calcUnitPrice: 747.0,
    },
    {
      id: 1138,
      sourceId: null,
      demandDate: '2025-10-02',
      usageType: 0,
      usageTime: 1,
      seats: 31,
      vehicleNum: 3,
      brand: '考斯特',
      route: '五四广场,青岛胶东国际机场',
      calcUnitPrice: 192.0,
    },
  ],
  attendants: [
    {
      id: 1125,
      sourceId: null,
      demandDate: '2025-10-01',
      type: 0,
      personNum: 1,
      duty: '摄影',
      calcUnitPrice: 1984.0,
    },
    {
      id: 1126,
      sourceId: null,
      demandDate: '2025-10-02',
      type: 3,
      personNum: 1,
      duty: '会议主持',
      calcUnitPrice: 1137.0,
    },
  ],
  activities: [
    {
      id: 547,
      sourceId: null,
      demandDate: '2025-10-01',
      demandUnitPrice: 60.0,
      personNum: 100,
      description: '拓展活动要求',
      calcUnitPrice: 60.0,
      paths: [
        '{"name":"拓展活动文件说明.pdf","url":"https://businessmanagement-test.haier.net/hbweb/file/download/obs-swszh1/1749448106-拓展活动文件说明.pdf"}',
      ],
    },
  ],
  insurances: [
    {
      id: 1123,
      sourceId: null,
      demandDate: '2025-10-01',
      demandUnitPrice: 22.0,
      personNum: 100,
      productId: 2,
      productMerchantId: 222,
      insuranceName: '大地保险22',
      insuranceContent: '大地保险保险内容1',
      calcUnitPrice: 22.0,
    },
    {
      id: 1124,
      sourceId: null,
      demandDate: '2025-10-02',
      demandUnitPrice: 11.0,
      personNum: 100,
      productId: 1,
      productMerchantId: 111,
      insuranceName: '平安保险11',
      insuranceContent: '平安保险内容1',
      calcUnitPrice: 11.0,
    },
  ],
  calcTotalPrice: 227680,
  demandTotalPrice: 2925657,
  material: {
    id: 529,
    sourceId: null,
    demandTotalPrice: 780.0,
    calcTotalPrice: 780.0,
    materialDetails: [
      {
        id: 2092,
        miceDemandMaterialId: 529,
        type: 0,
        specs: '条幅',
        num: 1,
        unit: '张',
        unitPrice: 30.0,
      },
      {
        id: 2093,
        miceDemandMaterialId: 529,
        type: 1,
        specs: '投影仪',
        num: 2,
        unit: '个',
        unitPrice: 100.0,
      },
      {
        id: 2094,
        miceDemandMaterialId: 529,
        type: 3,
        specs: '麦克',
        unit: '个',
        num: 1,
        unitPrice: 150.0,
      },
      {
        id: 2095,
        miceDemandMaterialId: 529,
        type: 2,
        specs: '音响',
        unit: '个',
        num: 4,
        unitPrice: 100.0,
      },
    ],
  },
  traffic: null,
  presents: [
    {
      id: 937,
      sourceId: null,
      deliveryDate: '2025-07-31',
      demandTotalPrice: 10000.0,
      personNum: 100,
      unit: '盒',
      personSpecs: '以服务商提报为准',
      productId: null,
      productMerchantId: null,
      productName: '坚果礼盒',
      optionType: null,
      unitPrice: null,
      calcTotalPrice: 10000.0,
    },
    {
      id: 938,
      sourceId: null,
      deliveryDate: '2025-07-31',
      demandTotalPrice: 53000.0,
      personNum: 100,
      unit: '件',
      personSpecs: '出厂年份：2020年，品类：特曲80版',
      productId: 1,
      productMerchantId: 4787,
      productName: '泸州老窖',
      optionType: 1,
      unitPrice: 530.0,
      calcTotalPrice: 53000.0,
    },
  ],
  others: [
    {
      id: 533,
      sourceId: null,
      demandDate: null,
      itemName: '门票',
      num: 100,
      unit: '张',
      specs: '以服务商提报为准',
      demandTotalPrice: 20000.0,
      calcTotalPrice: 20000.0,
    },
  ],
};
