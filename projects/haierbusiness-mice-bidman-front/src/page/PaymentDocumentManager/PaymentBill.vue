<!-- 缴费单 -->
<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Input as hInput,
  Upload as hUpload,
  Modal,
  message,
  TableProps,
  Table as ATable,
  Tabs as ATabs,
  TabPane as ATabPane,
  Textarea as ATextarea,
  DatePicker as hDatePicker,
  InputNumber as hInputNumber,
  Tooltip,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined, UploadOutlined } from '@ant-design/icons-vue';
import { paymentFromApi, fileApi } from '@haierbusiness-front/apis';
import { IPaymentFromFilter, IPaymentFrom } from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog } from '@haierbusiness-front/composables';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import router from '../../router';
import { PaymentFromStatusEnum, PaymentFromStatusMap } from '@haierbusiness-front/common-libs';
import Actions from '@haierbusiness-front/components/actions/Actions.vue';
import type { MenuItemType, MenuInfo } from 'ant-design-vue/lib/menu/src/interface';
// const router = useRouter()

const currentRouter = ref();
const store = applicationStore();
console.log(store.loginUser?.authorities, 'store');

// 权限判断：检查是否为会务顾问或会务负责人
const hasPaymentPermission = computed(() => {
  if (!store.loginUser?.authorities) return false;

  return store.loginUser.authorities.some((item) => item.authority === '211' || item.authority === '213');
});

// 权限判断：检查是否为会务顾问（会务顾问不能上传付款凭证）
const isConsultant = computed(() => {
  if (!store.loginUser?.authorities) return false;

  return store.loginUser.authorities.some((item) => item.authority === '211');
});

// 状态颜色映射
const getStatusColor = (status: number) => {
  switch (status) {
    case PaymentFromStatusEnum.PENDING_PAYMENT_UPLOAD:
      return 'orange';
    case PaymentFromStatusEnum.PENDING_FINANCIAL_CONFIRM:
      return 'blue';
    case PaymentFromStatusEnum.PAYMENT_REJECTED:
      return 'red';
    case PaymentFromStatusEnum.PENDING_INVOICE_UPLOAD:
      return 'cyan';
    case PaymentFromStatusEnum.APPROVAL_REJECTED:
      return 'volcano';
    case PaymentFromStatusEnum.APPROVING:
      return 'gold';
    case PaymentFromStatusEnum.COMPLETED:
      return 'green';
    default:
      return 'default';
  }
};

onMounted(async () => {
  currentRouter.value = await router;
  // 页面初始化时调用列表接口
  listApiRun({
    pageNum: 1,
    pageSize: 10,
  });
});

const columns: ColumnType[] = [
  {
    title: '结算单号',
    dataIndex: 'receivePaymentCode',
    width: '250px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '服务商名称',
    dataIndex: 'merchantName',
    width: '200px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '账单总金额',
    dataIndex: 'totalAmount',
    width: '200px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
  {
    title: '结算比例',
    dataIndex: 'settlementRate',
    width: '80px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => (text != null ? `${text}%` : ''),
  },
  {
    title: '收款金额',
    dataIndex: 'receivePaymentAmount',
    width: '200px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建人',
    dataIndex: 'createName',
    width: '100px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '200px',
    fixed: 'right',
    align: 'center',
  },
];
const searchParam = ref<IPaymentFromFilter>({});
const { data, run: listApiRun, loading, current, pageSize } = usePagination(paymentFromApi.getPaymentPage);

// 生成缴费单弹框的独立查询参数
const paymentOrderSearchParam = ref<{
  startTime?: string;
  endTime?: string;
  merchantCode?: string;
}>({});
const paymentOrderBeginAndEnd = ref<[Dayjs, Dayjs]>();

const reset = () => {
  searchParam.value = {};
  beginAndEnd.value = undefined;
};

const dataSource = computed(() => data.value?.records || []);

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total || 0,
  current: data.value?.pageNum || 1,
  pageSize: data.value?.pageSize || 10,
  style: { justifyContent: 'center' },
}));

const handleTableChange = (pag: { current: number; pageSize: number }, filters?: any, sorter?: any) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

// View变量已删除，功能合并到viewMode中
const detailVisible = ref(false);
const currentDetailRecord = ref<any>(null);
const detailLoading = ref(false);
const viewMode = ref<'view' | 'upload'>('view'); // 弹窗模式：查看或上传

// 上传发票相关
const invoiceModalVisible = ref(false);
const invoiceLoading = ref(false);
const currentInvoiceRecord = ref<any>(null);
const invoiceForm = ref({
  invoiceType: 1, // 默认选择国旅
  invoiceDate: '',
  invoiceNumber: '',
  invoiceAmount: undefined,
});

// 审批流相关
const approveCode = ref<string>('');
const approvalModalShow = ref<boolean>(false);
const businessProcess = import.meta.env.VITE_BUSINESS_PROCESS_URL;

//查看
const handleView = (record?: any) => {
  if (record && record.id) {
    // 有传入记录，获取详情数据
    detailLoading.value = true;
    viewMode.value = 'view';
    paymentFromApi
      .getPaymentDetails(record.id)
      .then((res) => {
        currentDetailRecord.value = res;
        detailVisible.value = true;
      })
      .catch((error) => {
        console.error('获取详情失败:', error);
        message.error('获取详情失败，请重试');
      })
      .finally(() => {
        detailLoading.value = false;
      });
  } else {
    // 关闭详情弹窗
    detailVisible.value = false;
    currentDetailRecord.value = null;
    fileList.value = [];
    ReasonsRejection.value = '';
  }
};

const beginAndEnd = ref<[Dayjs, Dayjs]>();
watch(
  () => beginAndEnd.value,
  (n: any, o: any) => {
    if (n) {
      searchParam.value.startTime = dayjs(n[0]).format('YYYY-MM-DD 00:00:00');
      searchParam.value.endTime = dayjs(n[1]).format('YYYY-MM-DD 23:59:59');
    } else {
      searchParam.value.startTime = undefined;
      searchParam.value.endTime = undefined;
    }
  },
);

// 监听生成缴费单弹框的时间选择器变化
watch(
  () => paymentOrderBeginAndEnd.value,
  (n: any, o: any) => {
    if (n) {
      paymentOrderSearchParam.value.startTime = dayjs(n[0]).format('YYYY-MM-DD 00:00:00');
      paymentOrderSearchParam.value.endTime = dayjs(n[1]).format('YYYY-MM-DD 23:59:59');
    } else {
      paymentOrderSearchParam.value.startTime = undefined;
      paymentOrderSearchParam.value.endTime = undefined;
    }
  },
);

// 上传付款凭证相关
const uploadLoading = ref(false);
const activeKey = ref('1');
const fileList = ref<any[]>([]);
const ReasonsRejection = ref('');
const baseUrl = import.meta.env.VITE_BUSINESS_URL;

// 打开上传付款凭证弹窗
const openUploadModal = (record: any) => {
  detailLoading.value = true;
  viewMode.value = 'upload';
  // 获取详情数据
  paymentFromApi
    .getPaymentDetails(record.id)
    .then((res) => {
      currentDetailRecord.value = res;
      detailVisible.value = true;
      fileList.value = [];
      ReasonsRejection.value = '';
    })
    .catch((error) => {
      console.error('获取详情失败:', error);
      message.error('获取详情失败，请重试');
    })
    .finally(() => {
      detailLoading.value = false;
    });
};

// 关闭上传弹窗（已合并到handleView函数中）

// 打开上传发票弹窗
const openInvoiceModal = (record: any) => {
  currentInvoiceRecord.value = record;
  invoiceModalVisible.value = true;
  // 重置表单
  invoiceForm.value = {
    invoiceType: 1,
    invoiceDate: '',
    invoiceNumber: '',
    invoiceAmount: undefined,
  };
};

// 关闭上传发票弹窗
const closeInvoiceModal = () => {
  invoiceModalVisible.value = false;
  currentInvoiceRecord.value = null;
  invoiceForm.value = {
    invoiceType: 1,
    invoiceDate: '',
    invoiceNumber: '',
    invoiceAmount: undefined,
  };
};

// 提交上传发票
const submitInvoice = () => {
  // 验证表单
  if (!invoiceForm.value.invoiceDate) {
    message.error('请选择发票日期');
    return;
  }
  if (!invoiceForm.value.invoiceNumber) {
    message.error('请输入发票号');
    return;
  }
  if (!invoiceForm.value.invoiceAmount || invoiceForm.value.invoiceAmount <= 0) {
    message.error('请输入有效的发票金额');
    return;
  }

  if (!currentInvoiceRecord.value?.id) {
    message.error('记录ID不存在');
    return;
  }

  invoiceLoading.value = true;

  // 调用uploadInvoice接口
  paymentFromApi
    .uploadInvoice({
      paymentId: currentInvoiceRecord.value.id,
      paymentCode: currentInvoiceRecord.value.receivePaymentCode,
      invoiceType: invoiceForm.value.invoiceType,
      invoiceDate: invoiceForm.value.invoiceDate,
      invoiceNumber: invoiceForm.value.invoiceNumber,
      invoiceAmount: invoiceForm.value.invoiceAmount,
    })
    .then((res) => {
      message.success('发票上传成功');
      closeInvoiceModal();
      
      // 显示审批流弹窗
      approvalModalShow.value = true;
      // 审批Code赋值
      approveCode.value = res.data;
      
      // 刷新列表
      listApiRun({
        ...searchParam.value,
        pageNum: data.value?.pageNum || 1,
        pageSize: data.value?.pageSize || 10,
      });
    })
    .catch((error) => {
      console.error('上传发票失败:', error);
      message.error('上传发票失败，请重试');
    })
    .finally(() => {
      invoiceLoading.value = false;
    });
};

// 关闭生成缴费单弹窗
const closePaymentOrderModal = () => {
  PaymentOrderVisible.value = false;
  settlementList.value = [];
  selectedRowKeys.value = [];
  // 重置弹窗中的查询条件
  paymentOrderBeginAndEnd.value = undefined;
  paymentOrderSearchParam.value = {};
};
//发票
const invoiceColumns: ColumnType<DataType>[] = [
  {
    title: '发票号',
    dataIndex: 'name',
  },
  {
    title: '发票日期',
    dataIndex: 'age',
  },
  {
    title: '发票金额',
    dataIndex: '',
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
];

// 详情页订单表格列
const detailOrderColumns: ColumnType<DataType>[] = [
  {
    title: '会议单号',
    dataIndex: 'mainCode',
    width: '150px',
    align: 'center',
  },
  {
    title: '会议时间',
    width: '200px',
    align: 'center',
    customRender: ({ record }) => {
      if (record.startDate && record.endDate) {
        return `${record.startDate} 至 ${record.endDate}`;
      }
      return '';
    },
  },
  {
    title: '会议负责人',
    dataIndex: 'operatorName',
    width: '120px',
    align: 'center',
  },
  {
    title: '账单金额',
    dataIndex: 'billAmount',
    width: '120px',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
  {
    title: '服务费率',
    dataIndex: 'feeRate',
    width: '100px',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text}%` : ''),
  },
  {
    title: '付款金额',
    dataIndex: 'receivePaymentAmount',
    width: '120px',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
];

// 发票类型映射
const InvoiceTypeMap = {
  1: '国旅',
  2: '服务商',
};

// 详情页发票表格列
const detailInvoiceColumns: ColumnType<DataType>[] = [
  {
    title: '发票号',
    dataIndex: 'invoiceNumber',
    align: 'center',
  },
  {
    title: '发票日期',
    dataIndex: 'invoiceDate',
    align: 'center',
  },
  {
    title: '发票类型',
    dataIndex: 'invoiceType',
    align: 'center',
    customRender: ({ text }) => InvoiceTypeMap[text as keyof typeof InvoiceTypeMap] || '未知类型',
  },
  {
    title: '发票金额',
    dataIndex: 'invoiceAmount',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
];

// 计算发票金额合计
const calculateInvoiceTotal = () => {
  if (!currentDetailRecord.value) return '0';
  const invoiceData = getInvoiceData();
  const total = invoiceData.reduce((sum: number, item: any) => sum + (item.invoiceAmount || 0), 0);
  return `${total}元`;
};

// 获取发票数据
const getInvoiceData = () => {
  if (!currentDetailRecord.value || !currentDetailRecord.value.miceInvoiceQueryDetails) {
    return [];
  }
  // 使用miceInvoiceQueryDetails作为发票数据源
  return currentDetailRecord.value.miceInvoiceQueryDetails.map((invoice: any, index: number) => ({
    key: invoice.id || index,
    invoiceNumber: invoice.invoiceNumber || '',
    invoiceDate: invoice.invoiceDate || '',
    invoiceAmount: invoice.invoiceAmount || 0,
    invoiceType: invoice.invoiceType || 1,
    isConfirmed: invoice.isConfirmed || 0,
  }));
};

//上传付款单
const PaymentOrderVisible = ref(false);
//选择的结算单
const settlementList = ref<any[]>([]);

const {
  data: PaymentOrderData,
  run: PaymentOrderlist,
  loading: paymentOrderLoading,
  current: paymentOrderCurrent,
  pageSize: paymentOrderPageSize,
} = usePagination(paymentFromApi.getBalnceList);

const handlePaymentOrder = () => {
  PaymentOrderlist({
    startTime: paymentOrderSearchParam.value.startTime,
    endTime: paymentOrderSearchParam.value.endTime,
    merchantCode: paymentOrderSearchParam.value.merchantCode,
    pageNum: 1,
    pageSize: 10,
  });
};

// PaymentOrder表格分页
const paymentOrderPagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: PaymentOrderData.value?.total || 0,
  current: PaymentOrderData.value?.pageNum || 1,
  pageSize: PaymentOrderData.value?.pageSize || 10,
  style: { justifyContent: 'center' },
}));

// 处理PaymentOrder表格分页变化
const handlePaymentOrderTableChange = (pag: any, filters?: any, sorter?: any) => {
  PaymentOrderlist({
    startTime: paymentOrderSearchParam.value.startTime,
    endTime: paymentOrderSearchParam.value.endTime,
    merchantCode: paymentOrderSearchParam.value.merchantCode,
    pageNum: pag.current || 1,
    pageSize: pag.pageSize || 10,
  });
};
const selectedRowKeys = ref<Key[]>([]);

const rowSelection: TableProps['rowSelection'] = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys: Key[], rows: DataType[]) => {
    selectedRowKeys.value = keys;
    settlementList.value = rows;
  },
};
//订单
const PaymentOrderColumns: ColumnType<DataType>[] = [
  {
    title: '会议单号',
    dataIndex: 'mainCode',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '会议时间',
    dataIndex: 'meetingTime',
    width: '200px',
    align: 'center',
    customRender: ({ record }) => {
      if (record.startDate && record.endDate) {
        return `${record.startDate} 至 ${record.endDate}`;
      }
      return '';
    },
  },
  {
    title: '会议负责人',
    dataIndex: 'operatorName',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '结算金额',
    dataIndex: 'amount',
    width: '120px',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
  {
    title: '服务费率',
    dataIndex: 'feeRate',
    width: '100px',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text}%` : ''),
  },
  {
    title: '收款金额',
    dataIndex: 'receiveAmount',
    width: '120px',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
];
// 文件上传处理
const uploadRequest = (options: any) => {
  uploadLoading.value = true;

  const formData = new FormData();
  formData.append('file', options.file);

  fileApi
    .upload(formData)
    .then((it) => {
      options.file.filePath = baseUrl + it.path;
      options.file.fileName = options.file.name;
      options.onProgress(100);
      options.onSuccess(it, options.file);

      // 确保文件被添加到列表中
      if (!fileList.value.some((f) => f.fileName === options.file.name)) {
        fileList.value.push(options.file);
      }
    })
    .catch((error) => {
      console.error('上传失败:', error);
      message.error('文件上传失败，请重试');
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

// 文件删除处理
const handleFileRemove = (file: any) => {
  const index = fileList.value.findIndex((f) => f.uid === file.uid);
  if (index > -1) {
    fileList.value.splice(index, 1);
  }
};

// 提交上传的付款凭证
const submitUpload = () => {
  if (fileList.value.length === 0) {
    message.error('请先上传付款凭证');
    return;
  }

  if (!currentDetailRecord.value?.id) {
    message.error('记录ID不存在');
    return;
  }

  // 提取文件路径
  const attachmentFiles = fileList.value.map((file) => file.filePath).filter(Boolean) as string[];

  if (attachmentFiles.length === 0) {
    message.error('文件上传未完成，请重试');
    return;
  }

  uploadLoading.value = true;

  // 调用confirmPaymentPayment接口
  paymentFromApi
    .confirmPaymentPayment({
      id: currentDetailRecord.value.id,
      attachmentFile: attachmentFiles,
    })
    .then(() => {
      message.success('付款凭证上传成功');
      handleView(); // 关闭弹窗
      // 刷新列表
      listApiRun({
        ...searchParam.value,
        pageNum: data.value?.pageNum || 1,
        pageSize: data.value?.pageSize || 10,
      });
    })
    .catch((error) => {
      console.error('提交付款凭证失败:', error);
      message.error('提交失败，请重试');
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

// 驳回操作
const submitReject = () => {
  if (!ReasonsRejection.value.trim()) {
    message.error('请填写驳回原因');
    return;
  }

  if (!currentDetailRecord.value?.id) {
    message.error('记录ID不存在');
    return;
  }

  uploadLoading.value = true;

  // 调用rejectPayment接口
  paymentFromApi
    .rejectPayment({
      id: currentDetailRecord.value.id,
      attachmentFile: [], // 驳回时可以传空数组
      rejectReason: ReasonsRejection.value.trim(), // 传递驳回原因
    })
    .then(() => {
      message.success('驳回成功');
      handleView(); // 关闭弹窗
      // 刷新列表
      listApiRun({
        ...searchParam.value,
        pageNum: data.value?.pageNum || 1,
        pageSize: data.value?.pageSize || 10,
      });
    })
    .catch((error) => {
      console.error('驳回失败:', error);
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

// 提交生成付款单
const submitPaymentOrder = () => {
  if (!settlementList.value || settlementList.value.length === 0) {
    message.error('请选择会议');
    return;
  }

  // 提取选中会议的balanceNo列表
  const balanceIds = settlementList.value.map((item: any) => item.id).filter(Boolean);

  if (balanceIds.length === 0) {
    message.error('选中的会议中没有有效的结算单号');
    return;
  }

  // 提取merchantCode并检查是否为同一服务商
  const merchantCodes = settlementList.value.map((item: any) => item.merchantCode).filter(Boolean);
  const uniqueMerchantCodes = [...new Set(merchantCodes)];

  if (uniqueMerchantCodes.length === 0) {
    message.error('选中的会议中没有有效的服务商信息');
    return;
  }

  if (uniqueMerchantCodes.length > 1) {
    message.error('只能选择同一个服务商的会议进行生成缴费单');
    return;
  }

  // 调用生成付款单接口
  paymentFromApi
    .createPayment({
      balanceIds: balanceIds,
      merchantCode: uniqueMerchantCodes[0] as string,
    })
    .then(() => {
      message.success('缴费单生成成功');
      closePaymentOrderModal();
      // 刷新列表
      listApiRun({
        ...searchParam.value,
        pageNum: data.value?.pageNum || 1,
        pageSize: data.value?.pageSize || 10,
      });
    })
    .catch((error) => {
      console.error('生成缴费单失败:', error);
      message.error('生成缴费单失败，请重试');
    });
};

// 处理菜单点击事件
const handleMenuClick = (record: any, e: MenuInfo) => {
  const key = e.key as string;
  switch (key) {
    case 'upload':
      openUploadModal(record);
      break;
    case 'uploadInvoice':
      openInvoiceModal(record);
      break;
    case 'confirm':
      // 确认收款操作
      confirmPayment(record);
      break;
    case 'resubmit':
      // 重新提交审批操作
      resubmitApproval(record);
      break;
    default:
      break;
  }
};

// 计算菜单选项
const getMenuOptions = (record: any) => {
  const options: MenuItemType[] = [];

  // 根据状态添加不同的操作选项
  if (record.status === PaymentFromStatusEnum.PENDING_PAYMENT_UPLOAD) {
    // 待上传支付凭证：已移除上传支付凭证功能
    // 不再显示上传付款凭证按钮
  } else if (record.status === PaymentFromStatusEnum.PENDING_FINANCIAL_CONFIRM) {
    // 待财务确认收款：不显示确认收款按钮
    // 已移除确认收款功能
  } else if (record.status === PaymentFromStatusEnum.PAYMENT_REJECTED) {
    // 收款驳回：已移除重新上传支付凭证功能
    // 不再显示重新上传凭证按钮
  } else if (record.status === PaymentFromStatusEnum.PENDING_INVOICE_UPLOAD) {
    // 待国旅上传发票：显示上传发票按钮
    options.push({
      key: 'uploadInvoice',
      label: '上传发票',
    });
  } else if (record.status === PaymentFromStatusEnum.APPROVAL_REJECTED) {
    // 审批驳回：可以重新提交审批
    options.push({
      key: 'resubmit',
      label: '重新提交',
    });
  }
  // 审批中和已完成状态只显示查看，不需要额外的菜单选项

  return options;
};

// 确认收款操作
const confirmPayment = (record: any) => {
  if (!record?.id) {
    message.error('记录ID不存在');
    return;
  }

  Modal.confirm({
    title: '确认收款',
    content: '确认已收到该笔付款？',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      // 调用确认收款接口
      paymentFromApi
        .confirmPayment({
          id: record.id,
          attachmentFile: [],
        })
        .then(() => {
          message.success('确认收款成功');
          // 刷新列表
          listApiRun({
            ...searchParam.value,
            pageNum: data.value?.pageNum || 1,
            pageSize: data.value?.pageSize || 10,
          });
        })
        .catch((error) => {
          console.error('确认收款失败:', error);
          message.error('确认收款失败，请重试');
        });
    },
  });
};

// 重新提交审批操作
const resubmitApproval = (record: any) => {
  if (!record?.id) {
    message.error('记录ID不存在');
    return;
  }

  Modal.confirm({
    title: '重新提交审批',
    content: '确认重新提交该记录进行审批？',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      // TODO: 重新提交审批接口尚未实现
      message.error('重新提交审批功能暂未实现');
      /*
      // 调用重新提交审批接口
      paymentFromApi
        .resubmitApproval({
          id: record.id,
        })
        .then(() => {
          message.success('重新提交审批成功');
          // 刷新列表
          listApiRun({
            ...searchParam.value,
            pageNum: data.value?.pageNum || 1,
            pageSize: data.value?.pageSize || 10,
          });
        })
        .catch((error: any) => {
          console.error('重新提交审批失败:', error);
          message.error('重新提交审批失败，请重试');
        });
      */
    },
  });
};

// 关闭审批弹窗
const closeApproval = () => {
  approvalModalShow.value = false;
  approveCode.value = '';
};

// 生成付款单
const generatePaymentOrder = () => {
  // 先清空之前的选择状态
  settlementList.value = [];
  selectedRowKeys.value = [];
  PaymentOrderVisible.value = true;
  // 打开弹窗时就调用接口获取数据
  PaymentOrderlist({
    startTime: paymentOrderSearchParam.value.startTime,
    endTime: paymentOrderSearchParam.value.endTime,
    merchantCode: paymentOrderSearchParam.value.merchantCode,
    pageNum: 1,
    pageSize: 10,
  });
};
</script>

<template>
  <div class="page-container">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px">
        <h-row :align="'middle'" class="search-row">
          <h-col :span="2" class="search-label">
            <label for="serviceProvider">服务商：</label>
          </h-col>
          <h-col :span="4">
            <h-input v-model:value="searchParam.merchantCode" placeholder="请输入服务商" allow-clear />
          </h-col>
          <h-col :span="3" class="search-label">
            <label for="createTime">缴费单创建时间：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="beginAndEnd" value-format="YYYY-MM-DD" style="width: 100%" allow-clear />
          </h-col>

          <h-col :span="2" class="search-label">
            <label for="status">状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select v-model:value="searchParam.status" placeholder="请选择状态" allow-clear style="width: 100%">
              <h-select-option :value="PaymentFromStatusEnum.PENDING_PAYMENT_UPLOAD">
                {{ PaymentFromStatusMap[PaymentFromStatusEnum.PENDING_PAYMENT_UPLOAD] }}
              </h-select-option>
              <h-select-option :value="PaymentFromStatusEnum.PENDING_FINANCIAL_CONFIRM">
                {{ PaymentFromStatusMap[PaymentFromStatusEnum.PENDING_FINANCIAL_CONFIRM] }}
              </h-select-option>
              <h-select-option :value="PaymentFromStatusEnum.PAYMENT_REJECTED">
                {{ PaymentFromStatusMap[PaymentFromStatusEnum.PAYMENT_REJECTED] }}
              </h-select-option>
              <h-select-option :value="PaymentFromStatusEnum.PENDING_INVOICE_UPLOAD">
                {{ PaymentFromStatusMap[PaymentFromStatusEnum.PENDING_INVOICE_UPLOAD] }}
              </h-select-option>
              <h-select-option :value="PaymentFromStatusEnum.APPROVAL_REJECTED">
                {{ PaymentFromStatusMap[PaymentFromStatusEnum.APPROVAL_REJECTED] }}
              </h-select-option>
              <h-select-option :value="PaymentFromStatusEnum.APPROVING">
                {{ PaymentFromStatusMap[PaymentFromStatusEnum.APPROVING] }}
              </h-select-option>
              <h-select-option :value="PaymentFromStatusEnum.COMPLETED">
                {{ PaymentFromStatusMap[PaymentFromStatusEnum.COMPLETED] }}
              </h-select-option>
            </h-select>
          </h-col>
        </h-row>
        <h-row :align="'middle'" class="search-row">
          <h-col :span="24" class="button-row">
            <h-button class="button-margin" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />
              查询
            </h-button>
          </h-col>
        </h-row>
        <h-row :align="'middle'" class="search-row">
          <h-col :span="12" class="button-row-left">
            <h-button v-if="hasPaymentPermission" type="primary" @click="generatePaymentOrder">
              <PlusOutlined />
              生成缴费单
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table
          :columns="columns"
          :row-key="(record) => record.id"
          :size="'small'"
          :data-source="dataSource"
          :pagination="pagination"
          :scroll="{ x: 1400 }"
          :loading="loading"
          @change="handleTableChange($event as any)"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'receivePaymentCode'">
              <Tooltip :title="record.receivePaymentCode">
                <div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap">{{ record.receivePaymentCode }}</div>
              </Tooltip>
            </template>
            <template v-if="column.dataIndex === 'merchantName'">
              <Tooltip :title="record.merchantName">
                <div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap">{{ record.merchantName }}</div>
              </Tooltip>
            </template>
            <template v-if="column.dataIndex === 'status'">
              <h-tag :color="getStatusColor(record.status)">
                {{ PaymentFromStatusMap[record.status as keyof typeof PaymentFromStatusMap] || '未知状态' }}
              </h-tag>
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <div class="operator-buttons">
                <h-button type="link" @click="handleView(record)">查看</h-button>
                <Actions
                  v-if="getMenuOptions(record).length > 0"
                  :menu-options="getMenuOptions(record)"
                  :on-menu-click="(e) => handleMenuClick(record, e)"
                >
                </Actions>
              </div>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>
    <!-- 生成付款单弹窗 -->
    <Modal
      v-model:open="PaymentOrderVisible"
      title="生成缴费单"
      :footer="null"
      @cancel="closePaymentOrderModal"
      width="60%"
    >
      <div>
        <h-row :align="'middle'" class="modal-search-row">
          <h-col :span="3" class="search-label">
            <label for="merchantCode">服务商名称：</label>
          </h-col>
          <h-col :span="5">
            <h-input v-model:value="paymentOrderSearchParam.merchantCode" placeholder="支持名字和V码搜索" allow-clear />
          </h-col>

          <h-col :span="4" class="search-label">
            <label for="createTime">会议时间：</label>
          </h-col>
          <h-col :span="6">
            <h-range-picker
              v-model:value="paymentOrderBeginAndEnd"
              value-format="YYYY-MM-DD"
              style="width: 100%"
              allow-clear
            />
          </h-col>

          <h-col :span="6" class="modal-search-button">
            <h-button type="primary" @click="handlePaymentOrder()">
              <SearchOutlined />
              查询
            </h-button>
          </h-col>
        </h-row>
        <div class="modal-table-container">
          <a-table
            :key="PaymentOrderData?.pageNum || 1"
            :row-key="(record) => record.id"
            :row-selection="rowSelection"
            :columns="PaymentOrderColumns"
            :data-source="PaymentOrderData?.records || []"
            :loading="paymentOrderLoading"
            :pagination="paymentOrderPagination"
            @change="handlePaymentOrderTableChange"
            class="modal-table"
            :scroll="{ x: 'max-content' }"
          >
            <template #bodyCell="{ column, text, record }">
              <template v-if="column.dataIndex === 'mainCode'">
                <Tooltip :title="text">
                  <div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap">{{ text }}</div>
                </Tooltip>
              </template>
            </template>
          </a-table>
        </div>
        <div class="modal-footer">
          <h-button class="button-margin" @click="closePaymentOrderModal">取消</h-button>
          <h-button type="primary" @click="submitPaymentOrder" :loading="paymentOrderLoading">生成缴费单</h-button>
        </div>
      </div>
    </Modal>

    <!-- 删除了独立的上传付款凭证弹窗，已合并到详情弹窗中 -->

    <!-- 上传发票弹窗 -->
    <Modal v-model:open="invoiceModalVisible" title="上传发票" :footer="null" @cancel="closeInvoiceModal" width="500px">
      <div class="modal-content">
        <div class="modal-info-item"><strong>收款单号：</strong>{{ currentInvoiceRecord?.receivePaymentCode }}</div>
        <div class="modal-info-item"><strong>服务商名称：</strong>{{ currentInvoiceRecord?.merchantName }}</div>

        <div class="invoice-form-item">
          <label class="invoice-form-label">发票类型：</label>
          <h-select v-model:value="invoiceForm.invoiceType" class="invoice-form-input">
            <h-select-option :value="1">国旅</h-select-option>
            <h-select-option :value="2">服务商</h-select-option>
          </h-select>
        </div>

        <div class="invoice-form-item">
          <label class="invoice-form-label">发票日期：<span class="required-mark">*</span></label>
          <h-date-picker
            v-model:value="invoiceForm.invoiceDate"
            class="invoice-form-input"
            placeholder="请选择发票日期"
            value-format="YYYY-MM-DD"
          />
        </div>

        <div class="invoice-form-item">
          <label class="invoice-form-label">发票号：<span class="required-mark">*</span></label>
          <h-input v-model:value="invoiceForm.invoiceNumber" placeholder="请输入发票号" class="invoice-form-input" />
        </div>

        <div class="invoice-form-item">
          <label class="invoice-form-label">发票金额：<span class="required-mark">*</span></label>
          <h-input-number
            v-model:value="invoiceForm.invoiceAmount"
            placeholder="请输入发票金额"
            class="invoice-form-input"
            :min="0"
            :precision="2"
          />
        </div>

        <div class="modal-footer">
          <h-button class="button-margin" @click="closeInvoiceModal">取消</h-button>
          <h-button type="primary" @click="submitInvoice" :loading="invoiceLoading">确定</h-button>
        </div>
      </div>
    </Modal>

    <!-- 缴费单详情/上传付款凭证弹窗 -->
    <Modal
      v-model:open="detailVisible"
      :title="viewMode === 'view' ? '缴费单详情' : '上传付款凭证'"
      :footer="null"
      @cancel="handleView()"
      width="800px"
      :loading="detailLoading"
    >
      <div v-if="currentDetailRecord" class="modal-content">
        <!-- 基本信息 -->
        <div class="modal-info">
          <div class="modal-info-item"><strong>收款单号：</strong>{{ currentDetailRecord.receivePaymentCode }}</div>
          <div class="modal-info-item"><strong>服务商名称：</strong>{{ currentDetailRecord.merchantName }}</div>
          <div class="modal-info-item"><strong>付款总金额：</strong>{{ currentDetailRecord.receivePaymentAmount }}元</div>
          <!-- 查看模式：显示已上传的付款凭证 -->
          <div v-if="viewMode === 'view'" class="modal-info-item">
            <strong>付款凭证：</strong>
            <template v-if="currentDetailRecord.attachmentFiles && currentDetailRecord.attachmentFiles.length > 0">
              <template v-for="(file, index) in currentDetailRecord.attachmentFiles" :key="index">
                <a :href="file.path" target="_blank" class="button-margin" style="color: #1890ff">
                  {{ file.path ? file.path.split('/').pop() || `付款凭证${index + 1}` : `付款凭证${index + 1}` }}
                </a>
              </template>
            </template>
            <span v-else>无</span>
          </div>
        </div>

        <!-- Tab页 -->
        <a-tabs v-model:activeKey="activeKey">
          <a-tab-pane key="1" tab="订单">
            <h-table
              :columns="detailOrderColumns"
              :data-source="currentDetailRecord.receivePaymentRecordsDetails || []"
              :pagination="false"
              size="small"
              bordered
              class="detail-table"
            >
            </h-table>
          </a-tab-pane>
          <a-tab-pane key="2" tab="发票">
            <div class="invoice-total"><strong>发票金额合计：</strong>{{ calculateInvoiceTotal() }}</div>
            <h-table
              :columns="detailInvoiceColumns"
              :data-source="getInvoiceData()"
              :pagination="false"
              size="small"
              bordered
              class="detail-table"
            >
            </h-table>
          </a-tab-pane>
        </a-tabs>

        <!-- 上传模式：显示上传区域和驳回原因 -->
        <div v-if="viewMode === 'upload'" class="modal-upload-section">
          <div class="modal-upload-item">
            <label class="invoice-form-label">付款凭证：</label>
            <h-upload
              v-model:fileList="fileList"
              :custom-request="uploadRequest"
              :multiple="true"
              :max-count="5"
              @remove="handleFileRemove"
              accept=".pdf, .doc, .docx, .jpg, .png, .jpeg, .xls, .xlsx"
              :show-upload-list="true"
            >
              <h-button :loading="uploadLoading">
                <UploadOutlined />
                上传文件
              </h-button>
            </h-upload>
          </div>
          <div class="modal-upload-item">
            <label class="invoice-form-label">驳回原因：</label>
            <a-textarea
              v-model:value="ReasonsRejection"
              show-count
              :maxlength="200"
              placeholder="请填写驳回原因（驳回时必填）"
              class="modal-textarea"
            />
          </div>
        </div>

        <!-- 底部按钮 -->
        <div class="modal-footer">
          <h-button class="button-margin" @click="handleView()">取消</h-button>
          <!-- 查看模式：只显示确定按钮 -->
          <h-button v-if="viewMode === 'view'" type="primary" @click="handleView()">确定</h-button>
          <!-- 上传模式：显示确定和驳回按钮 -->
          <template v-if="viewMode === 'upload'">
            <h-button class="button-margin" @click="submitReject" :loading="uploadLoading" danger> 驳回 </h-button>
            <h-button type="primary" @click="submitUpload" :loading="uploadLoading"> 确定 </h-button>
          </template>
        </div>
      </div>
    </Modal>

    <!-- 审批流弹窗 -->
    <Modal
      v-model:open="approvalModalShow"
      title="审批流程"
      :keyboard="false"
      :maskClosable="false"
      :closable="false"
      width="80%"
    >
      <div>
        <iframe width="100%" :src="businessProcess + '?code=' + approveCode + '#/detailsPcSt'" frameborder="0"></iframe>
      </div>
      <template #footer>
        <h-button @click="closeApproval">确定</h-button>
      </template>
    </Modal>
  </div>
</template>

<style scoped lang="less">
// 页面容器
.page-container {
  background-color: #ffff;
  height: 100%;
  width: 100%;
  padding: 10px 10px 0px 10px;
  overflow: auto;
}

// 查询条件行
.search-row {
  padding: 10px 10px 0px 10px;
}

// 标签样式
.search-label {
  text-align: right;
  padding-right: 10px;
}

// 按钮行
.button-row {
  text-align: right;
}

.button-row-left {
  text-align: left;
}

// 按钮间距
.button-margin {
  margin-right: 10px;
}

// 弹框查询条件
.modal-search-row {
  padding: 10px 10px 0px 10px;
}

.modal-search-button {
  text-align: left;
  padding-left: 10px;
}

// 表格样式
.modal-table {
  margin-top: 15px;
}

.detail-table {
  margin-top: 10px;
}

// 弹框内容
.modal-content {
  padding: 20px 0;
}

.modal-info {
  margin-bottom: 20px;
}

.modal-info-item {
  margin-bottom: 12px;
}

.modal-upload-section {
  margin-bottom: 20px;
}

.modal-upload-item {
  margin-bottom: 16px;
}

.modal-textarea {
  margin-top: 8px;
}

.modal-footer {
  text-align: right;
  margin-top: 20px;
}

// 发票相关
.invoice-total {
  margin-bottom: 16px;
}

.invoice-form-item {
  margin-bottom: 16px;
}

.invoice-form-label {
  font-weight: bold;
}

.invoice-form-input {
  width: 100%;
  margin-top: 8px;
}

.required-mark {
  color: red;
}

// 原有样式
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

.operator-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}

.operator-buttons :deep(.ant-btn) {
  padding: 0 4px;
  font-size: 14px;
}
:deep(table) {
  table-layout: auto !important; /* 恢复默认自适应布局 */
}
</style>
